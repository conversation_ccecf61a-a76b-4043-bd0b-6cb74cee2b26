import { create } from 'zustand'
import { ImageService, StyleService } from '../services'
import type { ImageItem, ImageList, Style, ImageStatus } from '../types'

interface ImageState {
  // 状态
  images: ImageItem[]
  styles: Style[]
  currentImage: ImageItem | null
  isLoading: boolean
  isUploading: boolean
  isGenerating: boolean
  error: string | null
  pagination: {
    page: number
    limit: number
    total: number
    hasMore: boolean
  }

  // 动作
  loadStyles: () => Promise<void>
  loadImages: (refresh?: boolean) => Promise<void>
  uploadImage: (imageUri: string) => Promise<string>
  generateImage: (imageId: string, styleId: number) => Promise<void>
  deleteImage: (id: number) => Promise<void>
  setCurrentImage: (image: ImageItem | null) => void
  clearError: () => void
  refreshImages: () => Promise<void>
  loadMoreImages: () => Promise<void>
}

export const useImageStore = create<ImageState>((set, get) => ({
  // 初始状态
  images: [],
  styles: [],
  currentImage: null,
  isLoading: false,
  isUploading: false,
  isGenerating: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    hasMore: true,
  },

  // 加载风格列表
  loadStyles: async () => {
    try {
      set({ isLoading: true, error: null })

      const styles = await StyleService.getStyles()

      set({
        styles,
        isLoading: false,
      })
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || '加载风格列表失败',
      })
    }
  },

  // 加载图片列表
  loadImages: async (refresh = false) => {
    try {
      const state = get()

      if (refresh) {
        set({
          isLoading: true,
          error: null,
          pagination: { ...state.pagination, page: 1 },
        })
      } else {
        set({ isLoading: true, error: null })
      }

      const page = refresh ? 1 : state.pagination.page
      const response = await ImageService.getImageList({
        page,
        limit: state.pagination.limit,
      })

      const newImages = refresh ? response.data : [...state.images, ...response.data]

      set({
        images: newImages,
        isLoading: false,
        pagination: {
          page: response.page,
          limit: response.limit,
          total: response.total,
          hasMore: response.data.length === response.limit,
        },
      })
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || '加载图片列表失败',
      })
    }
  },

  // 上传图片
  uploadImage: async (imageUri: string) => {
    try {
      set({ isUploading: true, error: null })

      const imageId = await ImageService.uploadImage(imageUri)

      set({ isUploading: false })

      return imageId
    } catch (error: any) {
      set({
        isUploading: false,
        error: error.message || '图片上传失败',
      })
      throw error
    }
  },

  // 生成图片
  generateImage: async (imageId: string, styleId: number) => {
    try {
      set({ isGenerating: true, error: null })

      const result = await ImageService.generateImage(imageId, styleId)

      // 将新生成的图片添加到列表开头
      const state = get()
      set({
        images: [result, ...state.images],
        isGenerating: false,
      })
    } catch (error: any) {
      set({
        isGenerating: false,
        error: error.message || '图片生成失败',
      })
      throw error
    }
  },

  // 删除图片
  deleteImage: async (id: number) => {
    try {
      set({ isLoading: true, error: null })

      await ImageService.deleteImage(id)

      // 从列表中移除删除的图片
      const state = get()
      const updatedImages = state.images.filter(img => img.id !== id)

      set({
        images: updatedImages,
        isLoading: false,
        currentImage: state.currentImage?.id === id ? null : state.currentImage,
      })
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || '删除图片失败',
      })
      throw error
    }
  },

  // 设置当前图片
  setCurrentImage: (image: ImageItem | null) => {
    set({ currentImage: image })
  },

  // 清除错误
  clearError: () => set({ error: null }),

  // 刷新图片列表
  refreshImages: async () => {
    return get().loadImages(true)
  },

  // 加载更多图片
  loadMoreImages: async () => {
    const state = get()
    if (!state.pagination.hasMore || state.isLoading) {
      return
    }

    set({
      pagination: {
        ...state.pagination,
        page: state.pagination.page + 1,
      },
    })

    return get().loadImages(false)
  },
}))
