import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { UserService } from '../services'
import { STORAGE_KEYS } from '../constants'
import type { UserInfo, LoginParams } from '../types'

interface AuthState {
  // 状态
  isLoggedIn: boolean
  user: UserInfo | null
  token: string | null
  isLoading: boolean
  error: string | null

  // 动作
  login: (params: LoginParams) => Promise<void>
  loginWithPhone: (phone: string, code: string, inviteCode?: string) => Promise<void>
  loginWithApple: (
    identityToken: string,
    authorizationCode: string,
    inviteCode?: string
  ) => Promise<void>
  logout: () => Promise<void>
  refreshUserInfo: () => Promise<void>
  updateUserInfo: (data: Partial<UserInfo>) => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void
  checkAuthStatus: () => Promise<boolean>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      isLoggedIn: false,
      user: null,
      token: null,
      isLoading: false,
      error: null,

      // 手机号登录
      loginWithPhone: async (phone: string, code: string, inviteCode?: string) => {
        try {
          set({ isLoading: true, error: null })

          const response = await UserService.loginWithPhone({
            phone,
            code,
            inviteCode,
          })

          // 保存token到AsyncStorage
          await AsyncStorage.setItem(STORAGE_KEYS.TOKEN, response.token)
          await AsyncStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(response.user))

          set({
            isLoggedIn: true,
            user: response.user,
            token: response.token,
            isLoading: false,
            error: null,
          })
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || '登录失败',
          })
          throw error
        }
      },

      // Apple ID登录
      loginWithApple: async (
        identityToken: string,
        authorizationCode: string,
        inviteCode?: string
      ) => {
        try {
          set({ isLoading: true, error: null })

          const response = await UserService.loginWithApple({
            identityToken,
            authorizationCode,
            inviteCode,
          })

          // 保存token到AsyncStorage
          await AsyncStorage.setItem(STORAGE_KEYS.TOKEN, response.token)
          await AsyncStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(response.user))

          set({
            isLoggedIn: true,
            user: response.user,
            token: response.token,
            isLoading: false,
            error: null,
          })
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Apple登录失败',
          })
          throw error
        }
      },

      // 通用登录方法
      login: async (params: LoginParams) => {
        if (params.phone && params.code) {
          return get().loginWithPhone(params.phone, params.code, params.inviteCode)
        } else if (params.identityToken && params.authorizationCode) {
          return get().loginWithApple(
            params.identityToken,
            params.authorizationCode,
            params.inviteCode
          )
        } else {
          throw new Error('Invalid login parameters')
        }
      },

      // 退出登录
      logout: async () => {
        try {
          set({ isLoading: true })

          // 调用后端登出接口
          await UserService.logout()

          // 清除本地存储
          await AsyncStorage.multiRemove([STORAGE_KEYS.TOKEN, STORAGE_KEYS.USER_INFO])

          set({
            isLoggedIn: false,
            user: null,
            token: null,
            isLoading: false,
            error: null,
          })
        } catch (error: any) {
          // 即使后端登出失败，也要清除本地状态
          await AsyncStorage.multiRemove([STORAGE_KEYS.TOKEN, STORAGE_KEYS.USER_INFO])

          set({
            isLoggedIn: false,
            user: null,
            token: null,
            isLoading: false,
            error: null,
          })
        }
      },

      // 刷新用户信息
      refreshUserInfo: async () => {
        try {
          set({ isLoading: true, error: null })

          const userInfo = await UserService.getUserInfo()

          // 更新本地存储
          await AsyncStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo))

          set({
            user: userInfo,
            isLoading: false,
          })
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || '获取用户信息失败',
          })
          throw error
        }
      },

      // 更新用户信息
      updateUserInfo: async (data: Partial<UserInfo>) => {
        try {
          set({ isLoading: true, error: null })

          const updatedUser = await UserService.updateUserInfo(data)

          // 更新本地存储
          await AsyncStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(updatedUser))

          set({
            user: updatedUser,
            isLoading: false,
          })
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || '更新用户信息失败',
          })
          throw error
        }
      },

      // 检查认证状态
      checkAuthStatus: async () => {
        try {
          const token = await AsyncStorage.getItem(STORAGE_KEYS.TOKEN)
          const userInfoStr = await AsyncStorage.getItem(STORAGE_KEYS.USER_INFO)

          if (token && userInfoStr) {
            const userInfo = JSON.parse(userInfoStr)

            // 验证token是否有效
            try {
              const freshUserInfo = await UserService.getUserInfo()
              set({
                isLoggedIn: true,
                user: freshUserInfo,
                token,
              })
              return true
            } catch (error) {
              // token无效，清除本地数据
              await AsyncStorage.multiRemove([STORAGE_KEYS.TOKEN, STORAGE_KEYS.USER_INFO])
              set({
                isLoggedIn: false,
                user: null,
                token: null,
              })
              return false
            }
          } else {
            set({
              isLoggedIn: false,
              user: null,
              token: null,
            })
            return false
          }
        } catch (error) {
          console.error('Check auth status error:', error)
          return false
        }
      },

      // 清除错误
      clearError: () => set({ error: null }),

      // 设置加载状态
      setLoading: (loading: boolean) => set({ isLoading: loading }),
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({
        isLoggedIn: state.isLoggedIn,
        user: state.user,
        token: state.token,
      }),
    }
  )
)
