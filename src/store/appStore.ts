import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { STORAGE_KEYS } from '../constants'

interface AppState {
  // 应用状态
  isFirstLaunch: boolean
  isLoading: boolean
  error: string | null
  networkStatus: 'online' | 'offline'

  // UI状态
  activeTab: string
  showLoginModal: boolean
  showPermissionModal: boolean

  // 设置
  settings: {
    enableNotifications: boolean
    autoSaveToAlbum: boolean
    imageQuality: 'low' | 'medium' | 'high'
    theme: 'light' | 'dark' | 'auto'
  }

  // 动作
  setFirstLaunch: (isFirst: boolean) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
  setNetworkStatus: (status: 'online' | 'offline') => void
  setActiveTab: (tab: string) => void
  setShowLoginModal: (show: boolean) => void
  setShowPermissionModal: (show: boolean) => void
  updateSettings: (settings: Partial<AppState['settings']>) => void
  resetApp: () => void
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // 初始状态
      isFirstLaunch: true,
      isLoading: false,
      error: null,
      networkStatus: 'online',
      activeTab: 'Home',
      showLoginModal: false,
      showPermissionModal: false,
      settings: {
        enableNotifications: true,
        autoSaveToAlbum: false,
        imageQuality: 'high',
        theme: 'auto',
      },

      // 设置首次启动状态
      setFirstLaunch: (isFirst: boolean) => {
        set({ isFirstLaunch: isFirst })
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      // 设置错误
      setError: (error: string | null) => {
        set({ error })
      },

      // 清除错误
      clearError: () => {
        set({ error: null })
      },

      // 设置网络状态
      setNetworkStatus: (status: 'online' | 'offline') => {
        set({ networkStatus: status })
      },

      // 设置活动标签
      setActiveTab: (tab: string) => {
        set({ activeTab: tab })
      },

      // 显示/隐藏登录模态框
      setShowLoginModal: (show: boolean) => {
        set({ showLoginModal: show })
      },

      // 显示/隐藏权限模态框
      setShowPermissionModal: (show: boolean) => {
        set({ showPermissionModal: show })
      },

      // 更新设置
      updateSettings: (newSettings: Partial<AppState['settings']>) => {
        const currentSettings = get().settings
        set({
          settings: {
            ...currentSettings,
            ...newSettings,
          },
        })
      },

      // 重置应用状态
      resetApp: () => {
        set({
          isFirstLaunch: true,
          isLoading: false,
          error: null,
          activeTab: 'Home',
          showLoginModal: false,
          showPermissionModal: false,
          settings: {
            enableNotifications: true,
            autoSaveToAlbum: false,
            imageQuality: 'high',
            theme: 'auto',
          },
        })
      },
    }),
    {
      name: 'app-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({
        isFirstLaunch: state.isFirstLaunch,
        settings: state.settings,
      }),
    }
  )
)
