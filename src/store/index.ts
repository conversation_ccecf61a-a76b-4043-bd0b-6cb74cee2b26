export { useAuthStore } from './authStore'
export { useImageStore } from './imageStore'
export { useAppStore } from './appStore'

// 可以在这里添加一些组合的hooks
import { useAuthStore } from './authStore'
import { useImageStore } from './imageStore'
import { useAppStore } from './appStore'
import { isVipUser } from '../utils/authUtils'

// 检查用户是否可以执行操作的hook - 参考小程序逻辑
export const useCanPerformAction = () => {
  const { isLoggedIn, user } = useAuthStore()
  const { setShowLoginModal } = useAppStore()

  const checkAndPerform = (action: () => void | Promise<void>) => {
    if (!isLoggedIn) {
      setShowLoginModal(true)
      return false
    }

    // 使用与小程序一致的积分检查逻辑
    if (user && user.points !== undefined && user.points <= 3 && !isVipUser(user)) {
      // 可以在这里显示积分不足的提示
      return false
    }

    action()
    return true
  }

  return { checkAndPerform, isLoggedIn, user }
}

// 初始化应用状态的hook
export const useInitializeApp = () => {
  const { checkAuthStatus } = useAuthStore()
  const { loadStyles } = useImageStore()
  const { setFirstLaunch, isFirstLaunch } = useAppStore()

  const initialize = async () => {
    try {
      // 检查认证状态
      await checkAuthStatus()

      // 加载基础数据
      await loadStyles()

      // 标记不是首次启动
      if (isFirstLaunch) {
        setFirstLaunch(false)
      }
    } catch (error) {
      console.error('App initialization error:', error)
    }
  }

  return { initialize, isFirstLaunch }
}
