import { Platform } from 'react-native'
import appleAuth, {
  AppleRequestOperation,
  AppleRequestScope,
  AppleCredentialState,
} from '@invertase/react-native-apple-authentication'
import type { UserInfo, AccessCheckResult } from '../types'
import { AccessAction } from '../types'

export { AccessAction }
import { ERROR_MESSAGES } from '../constants'

// 检查Apple登录是否可用
export const isAppleSignInAvailable = async (): Promise<boolean> => {
  if (Platform.OS !== 'ios') {
    return false
  }

  try {
    return appleAuth.isSupported
  } catch (error) {
    console.error('Check Apple Sign In availability error:', error)
    return false
  }
}

// Apple登录
export const signInWithApple = async (): Promise<{
  identityToken: string
  authorizationCode: string
  user?: {
    email?: string
    name?: {
      firstName?: string
      lastName?: string
    }
  }
}> => {
  try {
    const appleAuthRequestResponse = await appleAuth.performRequest({
      requestedOperation: AppleRequestOperation.LOGIN,
      requestedScopes: [AppleRequestScope.EMAIL, AppleRequestScope.FULL_NAME],
    })

    const { identityToken, authorizationCode, user } = appleAuthRequestResponse

    if (!identityToken || !authorizationCode) {
      throw new Error('Apple登录失败：缺少必要的认证信息')
    }

    return {
      identityToken,
      authorizationCode,
      user: user as any,
    }
  } catch (error: any) {
    console.error('Apple Sign In error:', error)

    if (error.code === '1001') {
      throw new Error('用户取消了Apple登录')
    } else if (error.code === '1000') {
      throw new Error('Apple登录失败：未知错误')
    } else {
      throw new Error(error.message || 'Apple登录失败')
    }
  }
}

// 检查Apple登录凭据状态
export const checkAppleCredentialState = async (userID: string): Promise<AppleCredentialState> => {
  try {
    return await appleAuth.getCredentialStateForUser(userID)
  } catch (error) {
    console.error('Check Apple credential state error:', error)
    return AppleCredentialState.REVOKED
  }
}

// 验证手机号格式
export const validatePhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

// 验证验证码格式
export const validateSmsCode = (code: string): boolean => {
  const codeRegex = /^\d{6}$/
  return codeRegex.test(code)
}

// 简单的VIP检查函数 - 完全参考小程序逻辑
const _isVipUser = (userInfo: UserInfo): boolean => {
  return !!userInfo.isActiveVip
}

// 检查用户访问权限
export const checkUserAccess = (userInfo: UserInfo | null): AccessCheckResult => {
  // 未登录
  if (!userInfo) {
    return {
      canUse: false,
      reason: ERROR_MESSAGES.LOGIN_REQUIRED,
      action: AccessAction.LOGIN,
    }
  }

  // 检查积分 - 使用统一的VIP判断逻辑
  if (userInfo.points !== undefined && userInfo.points <= 3 && !_isVipUser(userInfo)) {
    return {
      canUse: false,
      reason: ERROR_MESSAGES.INSUFFICIENT_POINTS,
      action: AccessAction.PURCHASE,
    }
  }

  // 可以使用
  return {
    canUse: true,
  }
}

// 格式化用户显示名称
export const formatUserDisplayName = (userInfo: UserInfo): string => {
  if (userInfo.nickName) {
    return userInfo.nickName
  }

  if (userInfo.phone) {
    // 隐藏手机号中间4位
    return userInfo.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }

  return '用户'
}

// 检查是否是VIP用户 - 完全参考小程序逻辑
export const isVipUser = (userInfo: UserInfo): boolean => {
  return !!userInfo.isActiveVip
}

// 格式化VIP到期时间 - 参考小程序逻辑
export const formatVipExpireTime = (vipExpireAt?: string): string => {
  // 如果没有到期时间，显示永久
  if (!vipExpireAt) {
    return '永久'
  }

  try {
    const expireDate = new Date(vipExpireAt)
    const now = new Date()

    // 如果已过期，显示已过期
    if (expireDate <= now) {
      return '已过期'
    }

    // 计算剩余天数
    const diffTime = expireDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    // 根据剩余天数显示不同格式
    if (diffDays <= 7) {
      return `${diffDays}天后到期`
    } else if (diffDays <= 30) {
      const weeks = Math.floor(diffDays / 7)
      return `${weeks}周后到期`
    } else {
      const months = Math.floor(diffDays / 30)
      return `${months}个月后到期`
    }
  } catch (error) {
    console.error('Format VIP expire time error:', error)
    return '永久'
  }
}

// 生成邀请链接 - 参考小程序格式
export const generateInviteLink = (inviteCode: string): string => {
  // 根据实际的应用分享链接格式来生成，与小程序保持一致
  return `https://convert.pub/invite?code=${inviteCode}`
}

// 从链接中提取邀请码
export const extractInviteCodeFromLink = (link: string): string | null => {
  try {
    const url = new URL(link)
    return url.searchParams.get('code')
  } catch (error) {
    console.error('Extract invite code error:', error)
    return null
  }
}
