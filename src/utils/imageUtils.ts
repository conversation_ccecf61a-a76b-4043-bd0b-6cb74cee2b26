import { Alert, PermissionsAndroid, Platform } from 'react-native'
import {
  launchImageLibrary,
  launchCamera,
  ImagePickerResponse,
  MediaType,
} from 'react-native-image-picker'
import { IMAGE_CONFIG, ERROR_MESSAGES } from '../constants'

export interface ImagePickerOptions {
  mediaType?: MediaType
  quality?: number
  maxWidth?: number
  maxHeight?: number
  includeBase64?: boolean
}

export interface SelectedImage {
  uri: string
  type: string
  fileName: string
  fileSize: number
}

// 检查图片文件大小
export const validateImageSize = (fileSize: number): boolean => {
  return fileSize <= IMAGE_CONFIG.MAX_SIZE
}

// 检查图片类型
export const validateImageType = (type: string): boolean => {
  return IMAGE_CONFIG.ALLOWED_TYPES.includes(type as any)
}

// 请求相机权限
export const requestCameraPermission = async (): Promise<boolean> => {
  if (Platform.OS === 'android') {
    try {
      const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.CAMERA, {
        title: '相机权限',
        message: '需要相机权限来拍摄照片',
        buttonNeutral: '稍后询问',
        buttonNegative: '取消',
        buttonPositive: '确定',
      })
      return granted === PermissionsAndroid.RESULTS.GRANTED
    } catch (err) {
      console.warn(err)
      return false
    }
  }
  return true
}

// 请求相册权限
export const requestPhotoLibraryPermission = async (): Promise<boolean> => {
  if (Platform.OS === 'android') {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
        {
          title: '相册权限',
          message: '需要相册权限来选择照片',
          buttonNeutral: '稍后询问',
          buttonNegative: '取消',
          buttonPositive: '确定',
        }
      )
      return granted === PermissionsAndroid.RESULTS.GRANTED
    } catch (err) {
      console.warn(err)
      return false
    }
  }
  return true
}

// 显示图片选择器选项
export const showImagePickerOptions = (): Promise<'camera' | 'library' | undefined> => {
  return new Promise(resolve => {
    Alert.alert(
      '选择图片',
      '请选择图片来源',
      [
        {
          text: '取消',
          style: 'cancel',
          onPress: () => resolve(undefined),
        },
        {
          text: '拍照',
          onPress: () => resolve('camera'),
        },
        {
          text: '从相册选择',
          onPress: () => resolve('library'),
        },
      ],
      { cancelable: true, onDismiss: () => resolve(undefined) }
    )
  })
}

// 从相机拍摄图片
export const pickImageFromCamera = async (
  options?: ImagePickerOptions
): Promise<SelectedImage | null> => {
  const hasPermission = await requestCameraPermission()
  if (!hasPermission) {
    throw new Error('相机权限被拒绝')
  }

  return new Promise((resolve, reject) => {
    launchCamera(
      {
        mediaType: 'photo',
        quality: IMAGE_CONFIG.QUALITY as any,
        maxWidth: 1920,
        maxHeight: 1920,
        ...options,
      },
      (response: ImagePickerResponse) => {
        if (response.didCancel) {
          resolve(null)
          return
        }

        if (response.errorMessage) {
          reject(new Error(response.errorMessage))
          return
        }

        const asset = response.assets?.[0]
        if (!asset || !asset.uri) {
          reject(new Error('未选择图片'))
          return
        }

        // 验证图片
        if (asset.fileSize && !validateImageSize(asset.fileSize)) {
          reject(new Error(ERROR_MESSAGES.IMAGE_TOO_LARGE))
          return
        }

        if (asset.type && !validateImageType(asset.type)) {
          reject(new Error(ERROR_MESSAGES.INVALID_IMAGE_TYPE))
          return
        }

        resolve({
          uri: asset.uri,
          type: asset.type || 'image/jpeg',
          fileName: asset.fileName || 'image.jpg',
          fileSize: asset.fileSize || 0,
        })
      }
    )
  })
}

// 从相册选择图片
export const pickImageFromLibrary = async (
  options?: ImagePickerOptions
): Promise<SelectedImage | null> => {
  const hasPermission = await requestPhotoLibraryPermission()
  if (!hasPermission) {
    throw new Error('相册权限被拒绝')
  }

  return new Promise((resolve, reject) => {
    launchImageLibrary(
      {
        mediaType: 'photo',
        quality: IMAGE_CONFIG.QUALITY as any,
        maxWidth: 1920,
        maxHeight: 1920,
        ...options,
      },
      (response: ImagePickerResponse) => {
        if (response.didCancel) {
          resolve(null)
          return
        }

        if (response.errorMessage) {
          reject(new Error(response.errorMessage))
          return
        }

        const asset = response.assets?.[0]
        if (!asset || !asset.uri) {
          reject(new Error('未选择图片'))
          return
        }

        // 验证图片
        if (asset.fileSize && !validateImageSize(asset.fileSize)) {
          reject(new Error(ERROR_MESSAGES.IMAGE_TOO_LARGE))
          return
        }

        if (asset.type && !validateImageType(asset.type)) {
          reject(new Error(ERROR_MESSAGES.INVALID_IMAGE_TYPE))
          return
        }

        resolve({
          uri: asset.uri,
          type: asset.type || 'image/jpeg',
          fileName: asset.fileName || 'image.jpg',
          fileSize: asset.fileSize || 0,
        })
      }
    )
  })
}

// 通用图片选择方法
export const pickImage = async (
  source?: 'camera' | 'library',
  options?: ImagePickerOptions
): Promise<SelectedImage | null> => {
  try {
    let selectedSource = source

    if (!selectedSource) {
      selectedSource = await showImagePickerOptions()
      if (!selectedSource) {
        return null
      }
    }

    if (selectedSource === 'camera') {
      return await pickImageFromCamera(options)
    } else {
      return await pickImageFromLibrary(options)
    }
  } catch (error) {
    console.error('Pick image error:', error)
    throw error
  }
}

// 计算图片显示尺寸（用于瀑布流）
export const calculateImageDimensions = (
  originalWidth: number,
  originalHeight: number,
  containerWidth: number
): { width: number; height: number } => {
  const aspectRatio = originalHeight / originalWidth
  const height = containerWidth * aspectRatio

  return {
    width: containerWidth,
    height,
  }
}

// 获取图片比例 - 返回高度/宽度的比例（用于计算显示高度）
export const getImageRatio = (ratio?: string): number => {
  if (!ratio) return 3 / 2 // 默认 3:2 比例

  // 支持多种格式：'2:3', '3:2', '1:1', '16:9' 等
  const parts = ratio.split(':')
  if (parts.length !== 2) return 3 / 2

  const [width, height] = parts.map(Number)
  if (isNaN(width) || isNaN(height) || width <= 0 || height <= 0) {
    return 3 / 2
  }

  // 返回高度/宽度比例，用于计算显示高度
  return height / width
}

// 获取常见比例的预设值
export const getCommonRatios = () => ({
  square: '1:1', // 正方形
  portrait: '2:3', // 竖版
  landscape: '3:2', // 横版
  wide: '16:9', // 宽屏
  tall: '9:16', // 高屏
})

// 根据比例类型获取比例字符串
export const getRatioByType = (
  type: 'square' | 'portrait' | 'landscape' | 'wide' | 'tall'
): string => {
  const ratios = getCommonRatios()
  return ratios[type] || ratios.portrait
}
