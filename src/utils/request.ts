import AsyncStorage from '@react-native-async-storage/async-storage'
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

import { BASE_URL, API_TIMEOUT, STORAGE_KEYS, ERROR_MESSAGES, API_PREFIX } from '../constants'
import type { ApiResponse } from '../types'

// 创建axios实例
const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: BASE_URL + API_PREFIX,
    timeout: API_TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
    },
  })

  // 请求拦截器
  instance.interceptors.request.use(
    async config => {
      // 添加认证token
      const token = await AsyncStorage.getItem(STORAGE_KEYS.TOKEN)
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }

      console.log('API Request:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        data: config.data,
        params: config.params,
      })

      return config
    },
    error => {
      console.error('Request interceptor error:', error)
      return Promise.reject(error)
      // eslint-disable-next-line comma-dangle
    }
  )

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse<ApiResponse<any>>) => {
      console.log('API Response:', {
        status: response.status,
        url: response.config.url,
        data: response.data,
      })

      // 检查业务状态码
      if (response.data && !response.data.success) {
        const error = new Error(response.data.message || ERROR_MESSAGES.UNKNOWN_ERROR)
        ;(error as any).code = 'BUSINESS_ERROR'
        ;(error as any).response = response
        return Promise.reject(error)
      }

      return response
    },
    async error => {
      console.error('API Error:', {
        message: error.message,
        status: error.response?.status,
        url: error.config?.url,
        data: error.response?.data,
      })

      // 处理401未授权错误
      if (error.response?.status === 401) {
        // 清除本地存储的认证信息
        await AsyncStorage.multiRemove([STORAGE_KEYS.TOKEN, STORAGE_KEYS.USER_INFO])

        // 可以在这里触发登录页面跳转
        // 注意：这里需要根据实际的导航方案来实现
        console.log('Token expired, need to re-login')
      }

      // 网络错误处理
      if (!error.response) {
        error.message = ERROR_MESSAGES.NETWORK_ERROR
      }

      return Promise.reject(error)
      // eslint-disable-next-line comma-dangle
    }
  )

  return instance
}

// 创建请求实例
const request = createAxiosInstance()

// 通用请求方法
export class HttpClient {
  static async get<T>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await request.get<ApiResponse<T>>(url, {
      params,
      ...config,
    })
    return response.data.data
  }

  static async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await request.post<ApiResponse<T>>(url, data, config)
    return response.data.data
  }

  static async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await request.put<ApiResponse<T>>(url, data, config)
    return response.data.data
  }

  static async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await request.delete<ApiResponse<T>>(url, config)
    return response.data.data
  }

  // 文件上传方法
  static async upload<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    const response = await request.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      ...config,
    })
    return response.data.data
  }

  // 下载文件方法
  static async download(url: string, config?: AxiosRequestConfig): Promise<Blob> {
    const response = await request.get(url, {
      responseType: 'blob',
      ...config,
    })
    return response.data
  }
}

export default HttpClient
