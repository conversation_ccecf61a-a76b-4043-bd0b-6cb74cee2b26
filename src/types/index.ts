// 基础响应类型
export interface ApiResponse<T> {
  success: boolean
  message: string
  data: T
}

// 分页数据类型
export interface PaginatedData<T> {
  total: number
  page: number
  limit: number
  data: T[]
}

// 用户相关类型
export interface UserInfo {
  id: number
  phone?: string
  nickName?: string
  avatarUrl?: string
  points?: number
  isVip?: boolean
  vipExpireAt?: string
  inviteCode?: string
  isActiveVip?: boolean
}

export interface LoginParams {
  phone?: string
  code?: string
  appleId?: string
  identityToken?: string
  authorizationCode?: string
  inviteCode?: string
}

export interface LoginResponse {
  token: string
  user: UserInfo
}

// 图片相关类型
export enum ImageStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export interface ImageItem {
  id: number
  userId: number
  originalUrls: string[]
  resultUrl: string | null
  resultUrlPreview: string | null
  styleName: string
  status: ImageStatus
  errorMessage: string | null
  taskId: string | null
  ratio?: string // 图片比例，如 '2:3', '3:2', '1:1'
  createdAt?: Date
  updatedAt?: Date
}

export interface ImageListParams {
  excludeStatuses?: string
  page?: number
  limit?: number
}

export type ImageList = PaginatedData<ImageItem>

// 风格相关类型
export interface Style {
  id: number
  name: string
  preview: string | null
  isActive: boolean
  sortOrder: number
  createdAt: string
  updatedAt: string
  ratio?: string
  isPopular?: boolean
}

// 套餐相关类型
export interface PackageItem {
  id: number
  name: string
  description: string
  points: number
  price: number
  priceCNY: number
  originalPrice: number
  originalPriceCNY: number
  imageUrl: string | null
  isPopular: boolean
  isActive: boolean
  vipDuration?: number
  sortOrder: number
  type: 'points' | 'vip' // 套餐类型：次数卡或VIP会员
  createdAt?: Date
  updatedAt?: Date
}

// 订单相关类型
export interface CreateOrderParams {
  packageId: number
  paymentMethod: 'wechat' | 'alipay' | 'apple'
}

export interface CreateOrderResponse {
  orderNo: string
  payment: {
    appId: string
    nonceStr: string
    orderNo: string
    package: string
    paySign: string
    signType: any
    timeStamp: string
  }
}

// 导航相关类型
export type RootStackParamList = {
  Login: undefined
  Main: undefined
  ImageDetail: { imageId: number }
  Membership: undefined
}

export type TabParamList = {
  Home: undefined
  Gallery: undefined
  Profile: undefined
}

// 权限检查相关类型
export enum AccessAction {
  REFRESH = 'refresh',
  PURCHASE = 'purchase',
  LOGIN = 'login',
}

export interface AccessCheckResult {
  canUse: boolean
  reason?: string
  action?: AccessAction
}

// 瀑布流相关类型
export interface ImageWithHeight {
  id: number
  url: string
  height: number
  name: string
  originalIndex: number
  preview: string
}

// 应用状态类型
export interface AppState {
  isLoading: boolean
  user: UserInfo | null
  token: string | null
  isLoggedIn: boolean
}

// 错误类型
export interface AppError {
  message: string
  code?: string | number
  details?: any
}
