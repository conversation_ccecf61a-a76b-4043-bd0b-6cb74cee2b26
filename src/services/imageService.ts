import HttpClient from '../utils/request'
import type { ImageItem, ImageList, ImageListParams, ImageStatus } from '../types'

export class ImageService {
  // 上传图片
  static async uploadImage(imageUri: string): Promise<string> {
    const formData = new FormData()
    formData.append('file', {
      uri: imageUri,
      type: 'image/jpeg',
      name: 'image.jpg',
    } as any)

    return HttpClient.upload<string>('/image/upload', formData)
  }

  // 生成图片
  static async generateImage(imageId: string, styleId: number): Promise<ImageItem> {
    return HttpClient.post<ImageItem>('/image/generate', {
      imageId,
      styleId,
    })
  }

  // 获取图片列表
  static async getImageList(params?: ImageListParams): Promise<ImageList> {
    return HttpClient.get<ImageList>('/image/list', params)
  }

  // 获取图片详情
  static async getImageDetail(id: number): Promise<ImageItem> {
    return HttpClient.get<ImageItem>(`/image/${id}`)
  }

  // 删除图片
  static async deleteImage(id: number): Promise<void> {
    return HttpClient.delete<void>(`/image/${id}`)
  }

  // 二次编辑图片
  static async editImage(imageId: number, prompt: string): Promise<ImageItem> {
    return HttpClient.post<ImageItem>('/image/edit', {
      imageId,
      prompt,
    })
  }

  // 获取图片生成进度
  static async getGenerationProgress(taskId: string): Promise<{
    status: ImageStatus
    progress: number
    resultUrl?: string
    errorMessage?: string
  }> {
    return HttpClient.get(`/image/progress/${taskId}`)
  }

  // 保存图片到相册
  static async saveToAlbum(imageUrl: string): Promise<void> {
    // 这里需要调用React Native的相册保存API
    // 实际实现会在组件中处理
    throw new Error('This method should be implemented in the component')
  }

  // 分享图片
  static async shareImage(imageId: number): Promise<{
    shareUrl: string
    shareText: string
  }> {
    return HttpClient.post(`/image/${imageId}/share`)
  }
}
