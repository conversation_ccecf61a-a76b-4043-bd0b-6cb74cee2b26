import { Platform } from 'react-native'
import type { PackageItem } from '@/types'

// 动态导入内购模块，避免在Expo Go中报错
let InAppPurchases: any = null
try {
  InAppPurchases = require('expo-in-app-purchases')
} catch (error) {
  console.log('InAppPurchases module not available in development environment')
}

export interface PurchaseResult {
  success: boolean
  transactionId?: string
  productId?: string
  error?: string
}

export interface ProductInfo {
  productId: string
  price: string
  priceAmountMicros: number
  priceCurrencyCode: string
  title: string
  description: string
}

export class InAppPurchaseService {
  private static isInitialized = false

  // 初始化内购服务
  static async initialize(): Promise<boolean> {
    try {
      // 检查平台支持
      if (Platform.OS !== 'ios') {
        console.log('In-app purchases only supported on iOS')
        return false
      }

      // 检查模块是否可用（开发环境检查）
      if (!InAppPurchases) {
        console.log('InAppPurchases module not available (development environment)')
        return false
      }

      if (this.isInitialized) {
        return true
      }

      const isAvailable = await InAppPurchases.isAvailableAsync()
      if (!isAvailable) {
        console.log('In-app purchases not available')
        return false
      }

      await InAppPurchases.connectAsync()
      this.isInitialized = true
      console.log('In-app purchases initialized successfully')
      return true
    } catch (error) {
      console.error('Failed to initialize in-app purchases:', error)
      return false
    }
  }

  // 获取产品信息
  static async getProducts(productIds: string[]): Promise<ProductInfo[]> {
    try {
      if (!InAppPurchases) {
        throw new Error('InAppPurchases module not available')
      }

      if (!this.isInitialized) {
        const initialized = await this.initialize()
        if (!initialized) {
          throw new Error('Failed to initialize in-app purchases')
        }
      }

      const { results } = await InAppPurchases.getProductsAsync(productIds)

      return results.map(product => ({
        productId: product.productId,
        price: product.price,
        priceAmountMicros: product.priceAmountMicros,
        priceCurrencyCode: product.priceCurrencyCode,
        title: product.title,
        description: product.description,
      }))
    } catch (error) {
      console.error('Failed to get products:', error)
      throw error
    }
  }

  // 购买产品
  static async purchaseProduct(productId: string): Promise<PurchaseResult> {
    try {
      if (!InAppPurchases) {
        throw new Error('InAppPurchases module not available')
      }

      if (!this.isInitialized) {
        const initialized = await this.initialize()
        if (!initialized) {
          throw new Error('Failed to initialize in-app purchases')
        }
      }

      const { results } = await InAppPurchases.purchaseItemAsync(productId)

      if (results && results.length > 0) {
        const purchase = results[0]

        if (purchase.acknowledged) {
          return {
            success: true,
            transactionId: purchase.transactionId,
            productId: purchase.productId,
          }
        } else {
          // 确认购买
          await InAppPurchases.finishTransactionAsync(purchase, true)
          return {
            success: true,
            transactionId: purchase.transactionId,
            productId: purchase.productId,
          }
        }
      }

      return {
        success: false,
        error: 'Purchase failed - no results',
      }
    } catch (error: any) {
      console.error('Purchase failed:', error)

      // 处理用户取消购买
      if (error.code === 'E_USER_CANCELLED') {
        return {
          success: false,
          error: 'User cancelled purchase',
        }
      }

      return {
        success: false,
        error: error.message || 'Purchase failed',
      }
    }
  }

  // 恢复购买
  static async restorePurchases(): Promise<PurchaseResult[]> {
    try {
      if (!this.isInitialized) {
        const initialized = await this.initialize()
        if (!initialized) {
          throw new Error('Failed to initialize in-app purchases')
        }
      }

      const { results } = await InAppPurchases.getPurchaseHistoryAsync()

      return results.map(purchase => ({
        success: true,
        transactionId: purchase.transactionId,
        productId: purchase.productId,
      }))
    } catch (error) {
      console.error('Failed to restore purchases:', error)
      throw error
    }
  }

  // 获取待处理的购买
  static async getPendingPurchases(): Promise<any[]> {
    try {
      if (!this.isInitialized) {
        const initialized = await this.initialize()
        if (!initialized) {
          return []
        }
      }

      const { results } = await InAppPurchases.getPurchaseHistoryAsync()
      return results.filter(purchase => !purchase.acknowledged)
    } catch (error) {
      console.error('Failed to get pending purchases:', error)
      return []
    }
  }

  // 完成交易
  static async finishTransaction(purchase: any, consumeItem: boolean = true): Promise<void> {
    try {
      await InAppPurchases.finishTransactionAsync(purchase, consumeItem)
    } catch (error) {
      console.error('Failed to finish transaction:', error)
      throw error
    }
  }

  // 断开连接
  static async disconnect(): Promise<void> {
    try {
      if (this.isInitialized) {
        await InAppPurchases.disconnectAsync()
        this.isInitialized = false
      }
    } catch (error) {
      console.error('Failed to disconnect in-app purchases:', error)
    }
  }

  // 将套餐转换为产品ID
  static getProductIdFromPackage(pkg: PackageItem): string {
    // 这里需要根据您的App Store Connect配置来映射
    // 例如: com.yourapp.points.100, com.yourapp.vip.monthly 等
    return `com.ghibli.ai.package.${pkg.id}`
  }

  // 验证购买（服务器端验证）
  static async verifyPurchase(
    transactionId: string,
    productId: string,
    receiptData?: string
  ): Promise<boolean> {
    try {
      // 这里需要调用您的后端API来验证购买
      // 后端会向Apple服务器验证收据的真实性
      const response = await fetch('/api/verify-purchase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionId,
          productId,
          receiptData,
          platform: 'ios',
        }),
      })

      const result = await response.json()
      return result.success
    } catch (error) {
      console.error('Failed to verify purchase:', error)
      return false
    }
  }
}
