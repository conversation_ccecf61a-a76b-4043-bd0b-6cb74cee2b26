import HttpClient from '../utils/request'
import type { PackageItem, CreateOrderParams, CreateOrderResponse } from '../types'

export class PackageService {
  // 获取套餐列表
  static async getPackageList(): Promise<PackageItem[]> {
    return HttpClient.get<PackageItem[]>('/packages')
  }

  // 获取套餐详情
  static async getPackageDetail(id: number): Promise<PackageItem> {
    return HttpClient.get<PackageItem>(`/packages/${id}`)
  }

  // 创建订单
  static async createOrder(params: CreateOrderParams): Promise<CreateOrderResponse> {
    return HttpClient.post<CreateOrderResponse>('/order/create', params)
  }

  // 查询订单状态
  static async getOrderStatus(orderNo: string): Promise<{
    status: 'pending' | 'paid' | 'failed' | 'cancelled'
    orderNo: string
    amount: number
    createdAt: string
    paidAt?: string
  }> {
    return HttpClient.get(`/order/${orderNo}/status`)
  }

  // 处理内购（iOS）
  static async processPurchase(params: {
    packageId: number
    transactionId: string
    platform: 'ios' | 'android'
    receiptData?: string
  }): Promise<{
    success: boolean
    points?: number
    vipDays?: number
  }> {
    return HttpClient.post('/purchase/process', params)
  }

  // 恢复购买（iOS）
  static async restorePurchases(): Promise<{
    success: boolean
    restoredPurchases: Array<{
      productId: string
      transactionId: string
      points: number
      vipDays?: number
    }>
  }> {
    return HttpClient.post('/purchase/restore')
  }

  // 获取订单历史
  static async getOrderHistory(params?: { page?: number; limit?: number }): Promise<{
    total: number
    page: number
    limit: number
    data: Array<{
      orderNo: string
      packageName: string
      amount: number
      status: string
      createdAt: string
      paidAt?: string
    }>
  }> {
    return HttpClient.get('/order/history', params)
  }
}
