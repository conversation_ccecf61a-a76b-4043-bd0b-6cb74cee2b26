import HttpClient from '../utils/request'
import type { PackageItem, CreateOrderParams, CreateOrderResponse } from '../types'

export class PackageService {
  // 获取套餐列表
  static async getPackageList(): Promise<PackageItem[]> {
    // 临时模拟数据，实际应该从API获取
    const mockData: PackageItem[] = [
      // 次数卡套餐
      {
        id: 1,
        name: '基础次数卡',
        description: '适合轻度使用，性价比高',
        points: 10,
        price: 9.9,
        priceCNY: 9.9,
        originalPrice: 12.9,
        originalPriceCNY: 12.9,
        imageUrl: null,
        isPopular: false,
        isActive: true,
        type: 'points',
        sortOrder: 1,
      },
      {
        id: 2,
        name: '标准次数卡',
        description: '最受欢迎的选择，物超所值',
        points: 30,
        price: 19.9,
        priceCNY: 19.9,
        originalPrice: 29.9,
        originalPriceCNY: 29.9,
        imageUrl: null,
        isPopular: true,
        isActive: true,
        type: 'points',
        sortOrder: 2,
      },
      {
        id: 3,
        name: '超值次数卡',
        description: '大容量次数包，更加划算',
        points: 100,
        price: 49.9,
        priceCNY: 49.9,
        originalPrice: 79.9,
        originalPriceCNY: 79.9,
        imageUrl: null,
        isPopular: false,
        isActive: true,
        type: 'points',
        sortOrder: 3,
      },
      // VIP会员套餐
      {
        id: 4,
        name: '月度会员',
        description: '无限次数使用，专享VIP特权',
        points: 0,
        price: 29.9,
        priceCNY: 29.9,
        originalPrice: 39.9,
        originalPriceCNY: 39.9,
        imageUrl: null,
        isPopular: false,
        isActive: true,
        vipDuration: 30,
        type: 'vip',
        sortOrder: 4,
      },
      {
        id: 5,
        name: '季度会员',
        description: '3个月无限使用，更加优惠',
        points: 0,
        price: 79.9,
        priceCNY: 79.9,
        originalPrice: 119.9,
        originalPriceCNY: 119.9,
        imageUrl: null,
        isPopular: true,
        isActive: true,
        vipDuration: 90,
        type: 'vip',
        sortOrder: 5,
      },
      {
        id: 6,
        name: '年度会员',
        description: '一年无限使用，最超值选择',
        points: 0,
        price: 199.9,
        priceCNY: 199.9,
        originalPrice: 359.9,
        originalPriceCNY: 359.9,
        imageUrl: null,
        isPopular: false,
        isActive: true,
        vipDuration: 365,
        type: 'vip',
        sortOrder: 6,
      },
    ]

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    return mockData
    // return HttpClient.get<PackageItem[]>('/packages')
  }

  // 获取套餐详情
  static async getPackageDetail(id: number): Promise<PackageItem> {
    return HttpClient.get<PackageItem>(`/packages/${id}`)
  }

  // 创建订单
  static async createOrder(params: CreateOrderParams): Promise<CreateOrderResponse> {
    return HttpClient.post<CreateOrderResponse>('/order/create', params)
  }

  // 查询订单状态
  static async getOrderStatus(orderNo: string): Promise<{
    status: 'pending' | 'paid' | 'failed' | 'cancelled'
    orderNo: string
    amount: number
    createdAt: string
    paidAt?: string
  }> {
    return HttpClient.get(`/order/${orderNo}/status`)
  }

  // 处理内购（iOS）
  static async processPurchase(params: {
    packageId: number
    transactionId: string
    platform: 'ios' | 'android'
    receiptData?: string
  }): Promise<{
    success: boolean
    points?: number
    vipDays?: number
  }> {
    return HttpClient.post('/purchase/process', params)
  }

  // 恢复购买（iOS）
  static async restorePurchases(): Promise<{
    success: boolean
    restoredPurchases: Array<{
      productId: string
      transactionId: string
      points: number
      vipDays?: number
    }>
  }> {
    return HttpClient.post('/purchase/restore')
  }

  // 获取订单历史
  static async getOrderHistory(params?: { page?: number; limit?: number }): Promise<{
    total: number
    page: number
    limit: number
    data: Array<{
      orderNo: string
      packageName: string
      amount: number
      status: string
      createdAt: string
      paidAt?: string
    }>
  }> {
    return HttpClient.get('/order/history', params)
  }
}
