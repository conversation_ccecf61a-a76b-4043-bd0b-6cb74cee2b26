import HttpClient from '../utils/request'
import type { LoginParams, LoginResponse, UserInfo } from '../types'

export class UserService {
  // 手机号登录
  static async loginWithPhone(params: {
    phone: string
    code: string
    inviteCode?: string
  }): Promise<LoginResponse> {
    return HttpClient.post<LoginResponse>('/user/login/phone', params)
  }

  // Apple ID登录
  static async loginWithApple(params: {
    identityToken: string
    authorizationCode: string
    inviteCode?: string
  }): Promise<LoginResponse> {
    return HttpClient.post<LoginResponse>('/user/login/apple', params)
  }

  // 发送验证码
  static async sendSmsCode(phone: string): Promise<void> {
    return HttpClient.post<void>('/user/sms/send', { phone })
  }

  // 获取用户信息
  static async getUserInfo(): Promise<UserInfo> {
    return HttpClient.get<UserInfo>('/user/info')
  }

  // 更新用户信息
  static async updateUserInfo(data: Partial<UserInfo>): Promise<UserInfo> {
    return HttpClient.put<UserInfo>('/user/info', data)
  }

  // 绑定邀请码
  static async bindInviteCode(inviteCode: string): Promise<void> {
    return HttpClient.post<void>('/user/invite/bind', { inviteCode })
  }

  // 获取邀请统计
  static async getInviteStats(): Promise<{
    inviteCount: number
    rewardPoints: number
  }> {
    return HttpClient.get('/user/invite/stats')
  }

  // 退出登录
  static async logout(): Promise<void> {
    return HttpClient.post<void>('/user/logout')
  }
}
