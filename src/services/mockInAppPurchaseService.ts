import { Platform } from 'react-native'
import type { PackageItem } from '@/types'

export interface PurchaseResult {
  success: boolean
  transactionId?: string
  productId?: string
  error?: string
}

export interface ProductInfo {
  productId: string
  price: string
  priceAmountMicros: number
  priceCurrencyCode: string
  title: string
  description: string
}

/**
 * 模拟内购服务 - 仅用于开发环境测试UI
 * 在真机上会使用真实的InAppPurchaseService
 */
export class MockInAppPurchaseService {
  private static isInitialized = false

  // 模拟初始化
  static async initialize(): Promise<boolean> {
    try {
      console.log('Mock InAppPurchase: Initializing...')
      await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟初始化延迟
      this.isInitialized = true
      console.log('Mock InAppPurchase: Initialized successfully')
      return true
    } catch (error) {
      console.error('Mock InAppPurchase: Failed to initialize:', error)
      return false
    }
  }

  // 模拟获取产品信息
  static async getProducts(productIds: string[]): Promise<ProductInfo[]> {
    try {
      console.log('Mock InAppPurchase: Getting products for:', productIds)
      await new Promise(resolve => setTimeout(resolve, 500)) // 模拟网络延迟

      return productIds.map(productId => ({
        productId,
        price: '¥6.00', // 模拟价格
        priceAmountMicros: 6000000, // 6元 = 6,000,000 微分
        priceCurrencyCode: 'CNY',
        title: this.getProductTitle(productId),
        description: this.getProductDescription(productId),
      }))
    } catch (error) {
      console.error('Mock InAppPurchase: Failed to get products:', error)
      throw error
    }
  }

  // 模拟购买产品
  static async purchaseProduct(productId: string): Promise<PurchaseResult> {
    try {
      console.log('Mock InAppPurchase: Purchasing product:', productId)
      await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟购买延迟

      // 模拟90%成功率
      const isSuccess = Math.random() > 0.1

      if (isSuccess) {
        const transactionId = `mock_transaction_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        
        console.log('Mock InAppPurchase: Purchase successful:', {
          transactionId,
          productId
        })

        return {
          success: true,
          transactionId,
          productId,
        }
      } else {
        console.log('Mock InAppPurchase: Purchase failed (simulated)')
        return {
          success: false,
          error: 'Simulated purchase failure',
        }
      }
    } catch (error: any) {
      console.error('Mock InAppPurchase: Purchase failed:', error)
      return {
        success: false,
        error: error.message || 'Purchase failed',
      }
    }
  }

  // 模拟恢复购买
  static async restorePurchases(): Promise<PurchaseResult[]> {
    try {
      console.log('Mock InAppPurchase: Restoring purchases...')
      await new Promise(resolve => setTimeout(resolve, 1500))

      // 模拟一些历史购买记录
      return [
        {
          success: true,
          transactionId: 'mock_restored_1',
          productId: 'com.ghibli.ai.package.1',
        },
        {
          success: true,
          transactionId: 'mock_restored_2',
          productId: 'com.ghibli.ai.package.2',
        }
      ]
    } catch (error) {
      console.error('Mock InAppPurchase: Failed to restore purchases:', error)
      throw error
    }
  }

  // 模拟获取待处理的购买
  static async getPendingPurchases(): Promise<any[]> {
    console.log('Mock InAppPurchase: Getting pending purchases...')
    return [] // 模拟没有待处理的购买
  }

  // 模拟完成交易
  static async finishTransaction(purchase: any, consumeItem: boolean = true): Promise<void> {
    console.log('Mock InAppPurchase: Finishing transaction:', purchase.transactionId)
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  // 模拟断开连接
  static async disconnect(): Promise<void> {
    console.log('Mock InAppPurchase: Disconnecting...')
    this.isInitialized = false
  }

  // 将套餐转换为产品ID
  static getProductIdFromPackage(pkg: PackageItem): string {
    return `com.ghibli.ai.package.${pkg.id}`
  }

  // 模拟验证购买
  static async verifyPurchase(
    transactionId: string,
    productId: string,
    receiptData?: string
  ): Promise<boolean> {
    try {
      console.log('Mock InAppPurchase: Verifying purchase:', { transactionId, productId })
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟95%验证成功率
      const isValid = Math.random() > 0.05
      console.log('Mock InAppPurchase: Verification result:', isValid)
      
      return isValid
    } catch (error) {
      console.error('Mock InAppPurchase: Failed to verify purchase:', error)
      return false
    }
  }

  // 辅助方法：获取产品标题
  private static getProductTitle(productId: string): string {
    const titles: { [key: string]: string } = {
      'com.ghibli.ai.package.1': '100积分包',
      'com.ghibli.ai.package.2': '500积分包',
      'com.ghibli.ai.package.3': '1000积分包',
      'com.ghibli.ai.package.4': 'VIP月卡',
      'com.ghibli.ai.package.5': 'VIP年卡',
    }
    return titles[productId] || '未知产品'
  }

  // 辅助方法：获取产品描述
  private static getProductDescription(productId: string): string {
    const descriptions: { [key: string]: string } = {
      'com.ghibli.ai.package.1': '获得100个创作积分，用于AI图片生成',
      'com.ghibli.ai.package.2': '获得500个创作积分，用于AI图片生成',
      'com.ghibli.ai.package.3': '获得1000个创作积分，用于AI图片生成',
      'com.ghibli.ai.package.4': '30天VIP会员，享受无限创作',
      'com.ghibli.ai.package.5': '365天VIP会员，享受无限创作',
    }
    return descriptions[productId] || '未知产品描述'
  }

  // 检查是否为开发环境
  static isDevelopmentEnvironment(): boolean {
    return __DEV__ || Platform.OS !== 'ios'
  }
}
