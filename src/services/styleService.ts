import HttpClient from '../utils/request'
import type { Style } from '../types'

export class StyleService {
  // 获取所有风格列表
  static async getStyles(): Promise<Style[]> {
    return HttpClient.get<Style[]>('/styles')
  }

  // 获取热门风格
  static async getPopularStyles(): Promise<Style[]> {
    return HttpClient.get<Style[]>('/styles/popular')
  }

  // 获取风格详情
  static async getStyleDetail(id: number): Promise<Style> {
    return HttpClient.get<Style>(`/styles/${id}`)
  }

  // 搜索风格
  static async searchStyles(keyword: string): Promise<Style[]> {
    return HttpClient.get<Style[]>('/styles/search', { keyword })
  }
}
