import { useState, useCallback } from 'react'
import { Alert, ToastAndroid, Platform } from 'react-native'

export type ToastType = 'success' | 'error' | 'warning' | 'info'

interface ToastOptions {
  duration?: number
  position?: 'top' | 'center' | 'bottom'
}

interface ToastState {
  visible: boolean
  message: string
  type: ToastType
  duration: number
}

export const useToast = () => {
  const [toastState, setToastState] = useState<ToastState>({
    visible: false,
    message: '',
    type: 'info',
    duration: 3000,
  })

  const showToast = useCallback(
    (message: string, type: ToastType = 'info', options: ToastOptions = {}) => {
      const { duration = 3000 } = options

      if (Platform.OS === 'android') {
        // Android使用原生Toast
        ToastAndroid.show(message, ToastAndroid.SHORT)
      } else {
        // iOS使用自定义Toast或Alert
        setToastState({
          visible: true,
          message,
          type,
          duration,
        })

        // 自动隐藏
        setTimeout(() => {
          setToastState(prev => ({ ...prev, visible: false }))
        }, duration)
      }
    },
    []
  )

  const showSuccess = useCallback(
    (message: string, options?: ToastOptions) => {
      showToast(message, 'success', options)
    },
    [showToast]
  )

  const showError = useCallback(
    (message: string, options?: ToastOptions) => {
      showToast(message, 'error', options)
    },
    [showToast]
  )

  const showWarning = useCallback(
    (message: string, options?: ToastOptions) => {
      showToast(message, 'warning', options)
    },
    [showToast]
  )

  const showInfo = useCallback(
    (message: string, options?: ToastOptions) => {
      showToast(message, 'info', options)
    },
    [showToast]
  )

  const hideToast = useCallback(() => {
    setToastState(prev => ({ ...prev, visible: false }))
  }, [])

  // 显示确认对话框
  const showConfirm = useCallback(
    (title: string, message: string, onConfirm: () => void, onCancel?: () => void) => {
      Alert.alert(title, message, [
        {
          text: '取消',
          style: 'cancel',
          onPress: onCancel,
        },
        {
          text: '确定',
          onPress: onConfirm,
        },
      ])
    },
    []
  )

  // 显示选择对话框
  const showActionSheet = useCallback(
    (
      title: string,
      options: Array<{
        text: string
        onPress: () => void
        style?: 'default' | 'cancel' | 'destructive'
      }>
    ) => {
      Alert.alert(
        title,
        undefined,
        options.map(option => ({
          text: option.text,
          onPress: option.onPress,
          style: option.style || 'default',
        }))
      )
    },
    []
  )

  return {
    toastState,
    showToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    hideToast,
    showConfirm,
    showActionSheet,
  }
}
