import { useEffect, useState, useMemo } from 'react'
import { Dimensions } from 'react-native'
import type { ImageItem, Style, ImageWithHeight } from '@/types'
import { getImageRatio } from '@/utils/imageUtils'
import { IMAGE_CONFIG } from '@/constants'

interface UseWaterfallProps {
  imageList: (ImageItem | Style)[]
  imgKey: 'preview' | 'resultUrl' | 'resultUrlPreview'
  columnCount?: number
  gutter?: number
}

export function useWaterfall({
  imageList,
  imgKey,
  columnCount = IMAGE_CONFIG.WATERFALL_COLUMNS,
  gutter = IMAGE_CONFIG.WATERFALL_GUTTER,
}: UseWaterfallProps) {
  const [columns, setColumns] = useState<ImageWithHeight[][]>(
    Array(columnCount)
      .fill(0)
      .map(() => [])
  )
  const [heights, setHeights] = useState(Array(columnCount).fill(0))

  // 获取屏幕宽度
  const screenWidth = Dimensions.get('window').width
  const displayWidth = (screenWidth - gutter * (columnCount + 1)) / columnCount

  // 处理图片列表
  const processedImageList = useMemo(() => {
    return imageList.map((img, index) => {
      let url = ''
      let preview = ''
      let name = ''
      let ratio = 3 / 2 // 默认比例

      if ('preview' in img) {
        // Style类型
        url = img.preview || ''
        preview = img.preview || ''
        name = img.name || ''
        // 解析比例字符串，支持 "2:3", "3:2", "1:1" 等格式
        ratio = getImageRatio(img.ratio)
      } else {
        // ImageItem类型
        if (imgKey === 'resultUrl') {
          url = img.resultUrl || ''
          preview = img.resultUrlPreview || img.resultUrl || ''
        } else {
          url = img.resultUrlPreview || ''
          preview = img.resultUrlPreview || ''
        }
        name = img.styleName || ''
        // 对于生成的图片，也可能有不同比例
        ratio = getImageRatio(img.ratio) || 2 / 3 // 默认图片比例
      }

      return {
        id: img.id,
        url,
        preview,
        name,
        originalIndex: 'sortOrder' in img ? img.sortOrder : index,
        ratio,
      }
    })
  }, [imageList, imgKey])

  useEffect(() => {
    if (!processedImageList.length) {
      setColumns(
        Array(columnCount)
          .fill(0)
          .map(() => [])
      )
      setHeights(Array(columnCount).fill(0))
      return
    }

    // 重置状态 - 参考小程序实现
    const initialColumns: ImageWithHeight[][] = Array(columnCount)
      .fill(0)
      .map(() => [])
    setColumns(initialColumns)
    setHeights(Array(columnCount).fill(0))

    // 处理每张图片 - 使用小程序的状态更新方式
    processedImageList.forEach(img => {
      const displayHeight = displayWidth * img.ratio

      setHeights(prevHeights => {
        const minIndex = prevHeights.indexOf(Math.min(...prevHeights))
        const newHeights = [...prevHeights]
        newHeights[minIndex] += displayHeight + gutter

        setColumns(prevColumns => {
          const newColumns = [...prevColumns]
          // 确保 newColumns[minIndex] 是一个数组
          if (!Array.isArray(newColumns[minIndex])) {
            newColumns[minIndex] = []
          }
          newColumns[minIndex] = [
            ...newColumns[minIndex],
            {
              id: img.id,
              url: img.url,
              height: displayHeight,
              name: img.name,
              preview: img.preview,
              originalIndex: img.originalIndex,
            },
          ]
          return newColumns
        })

        return newHeights
      })
    })
  }, [processedImageList, columnCount, gutter, displayWidth])

  // 对每列进行排序，确保按照原始顺序显示
  const sortedColumns = useMemo(() => {
    return columns.map(column => [...column].sort((a, b) => a.originalIndex - b.originalIndex))
  }, [columns])

  return {
    columns: sortedColumns,
    columnWidth: displayWidth,
    gutter,
  }
}
