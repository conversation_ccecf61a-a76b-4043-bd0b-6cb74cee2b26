import React from 'react'
import { render } from '@testing-library/react-native'
import { ProfileScreen } from '../screens/Profile/ProfileScreen'

// Mock dependencies
jest.mock('@/store', () => ({
  useAuthStore: () => ({
    user: null,
    logout: jest.fn(),
    isLoggedIn: false,
  }),
}))

jest.mock('@/hooks', () => ({
  useToast: () => ({
    showSuccess: jest.fn(),
    showConfirm: jest.fn(),
    showError: jest.fn(),
  }),
}))

jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({ top: 0, bottom: 0, left: 0, right: 0 }),
}))

const mockNavigation = {
  navigate: jest.fn(),
}

describe('ProfileScreen', () => {
  it('renders correctly with NativeWind classes', () => {
    const { getByText } = render(<ProfileScreen navigation={mockNavigation} />)
    
    // Check if the screen renders without crashing
    expect(getByText('未登录')).toBeTruthy()
    expect(getByText('美图设计室')).toBeTruthy()
    expect(getByText('我的空间')).toBeTruthy()
  })

  it('displays VIP card correctly', () => {
    const { getByText } = render(<ProfileScreen navigation={mockNavigation} />)
    
    expect(getByText('💎 VIP')).toBeTruthy()
    expect(getByText('立即开通')).toBeTruthy()
  })

  it('displays menu items correctly', () => {
    const { getByText } = render(<ProfileScreen navigation={mockNavigation} />)
    
    expect(getByText('我的空间')).toBeTruthy()
    expect(getByText('我的收藏')).toBeTruthy()
    expect(getByText('帮助与反馈')).toBeTruthy()
    expect(getByText('自动消耗')).toBeTruthy()
    expect(getByText('邀请有礼')).toBeTruthy()
  })
})
