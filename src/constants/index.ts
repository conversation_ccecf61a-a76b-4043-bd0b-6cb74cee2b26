// API相关常量
export const BASE_URL = 'https://convert.pub' // 替换为实际的API地址
export const API_TIMEOUT = 10000
export const API_PREFIX = '/ai/api'

// 存储键名
export const STORAGE_KEYS = {
  TOKEN: 'auth_token',
  USER_INFO: 'user_info',
  SUBSCRIBE_MODAL_SHOWN: 'subscribe_modal_shown',
  FIRST_LAUNCH: 'first_launch',
} as const

// 图片相关常量
export const IMAGE_CONFIG = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  QUALITY: 0.8,
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/jpg'],
  WATERFALL_COLUMNS: 2,
  WATERFALL_GUTTER: 12,
} as const

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
} as const

// 应用配置
export const APP_CONFIG = {
  NAME: '吉卜力风格AI',
  VERSION: '1.0.0',
  SUPPORT_EMAIL: '<EMAIL>',
  PRIVACY_URL: 'https://example.com/privacy',
  TERMS_URL: 'https://example.com/terms',
} as const

// 颜色主题
export const COLORS = {
  PRIMARY: '#0fe0ff',
  SECONDARY: '#a3a3a3',
  BACKGROUND: '#0a0e17',
  SURFACE: '#1a1e27',
  TEXT_PRIMARY: '#ffffff',
  TEXT_SECONDARY: '#a3a3a3',
  ERROR: '#ff4444',
  SUCCESS: '#00ff88',
  WARNING: '#ffaa00',
  BORDER: '#333333',
} as const

// 字体大小
export const FONT_SIZES = {
  XS: 12,
  SM: 14,
  MD: 16,
  LG: 18,
  XL: 20,
  XXL: 24,
  XXXL: 32,
} as const

// 间距
export const SPACING = {
  XS: 4,
  SM: 8,
  MD: 16,
  LG: 24,
  XL: 32,
  XXL: 48,
} as const

// 动画配置
export const ANIMATION = {
  DURATION_SHORT: 200,
  DURATION_MEDIUM: 300,
  DURATION_LONG: 500,
} as const

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  LOGIN_REQUIRED: '请先登录',
  INSUFFICIENT_POINTS: '积分不足',
  IMAGE_TOO_LARGE: '图片文件过大',
  INVALID_IMAGE_TYPE: '不支持的图片格式',
  UPLOAD_FAILED: '图片上传失败',
  GENERATION_FAILED: '图片生成失败',
  UNKNOWN_ERROR: '未知错误，请稍后重试',
} as const

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  LOGOUT_SUCCESS: '退出登录成功',
  UPLOAD_SUCCESS: '上传成功',
  GENERATION_SUCCESS: '生成成功',
  SAVE_SUCCESS: '保存成功',
  DELETE_SUCCESS: '删除成功',
} as const

// 登录方式
export const LOGIN_TYPES = {
  PHONE: 'phone',
  APPLE: 'apple',
} as const

// 图片状态文本
export const IMAGE_STATUS_TEXT = {
  pending: '等待处理',
  processing: '处理中',
  completed: '已完成',
  failed: '处理失败',
} as const

// 默认头像
export const DEFAULT_AVATAR = 'https://example.com/default-avatar.png'

// 分享配置
export const SHARE_CONFIG = {
  TITLE: '吉卜力风格AI - 让你的照片更有创意',
  DESCRIPTION: '使用AI技术将你的照片转换成吉卜力动画风格',
  IMAGE_URL: 'https://cdn-img-cn.convert.pub/styles/share.png',
} as const
