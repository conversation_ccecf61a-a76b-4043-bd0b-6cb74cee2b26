import React, { useEffect, useState } from 'react'
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  FlatList,
  RefreshControl,
  StatusBar,
} from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { useImageStore, useAuthStore } from '@/store'
import { useToast } from '@/hooks'
import { Loading, ConfirmModal } from '@/components/ui'
import { GalleryWaterfallGrid } from '@/components/common'
import { COLORS, FONT_SIZES, SPACING, IMAGE_STATUS_TEXT } from '@/constants'
import type { ImageItem } from '@/types'

interface GalleryScreenProps {
  navigation: any
}

export const GalleryScreen: React.FC<GalleryScreenProps> = ({ navigation }) => {
  const [refreshing, setRefreshing] = useState(false)
  const [selectedImage, setSelectedImage] = useState<ImageItem | null>(null)
  const [showDeleteModal, setShowDeleteModal] = useState(false)

  const {
    images,
    isLoading,
    error,
    pagination,
    loadImages,
    deleteImage,
    clearError,
    refreshImages,
    loadMoreImages,
  } = useImageStore()

  const { isLoggedIn } = useAuthStore()
  const { showError, showSuccess } = useToast()

  useEffect(() => {
    // 只有登录用户才加载图片
    if (isLoggedIn) {
      loadImages(true)
    }
  }, [isLoggedIn])

  useEffect(() => {
    if (error) {
      showError(error)
      clearError()
    }
  }, [error])

  const handleRefresh = async () => {
    if (!isLoggedIn) return

    setRefreshing(true)
    try {
      await refreshImages()
    } finally {
      setRefreshing(false)
    }
  }

  const handleLoadMore = () => {
    if (isLoggedIn && pagination.hasMore && !isLoading) {
      loadMoreImages()
    }
  }

  const handleImagePress = (image: ImageItem) => {
    // 可以导航到图片详情页面
    // navigation.navigate('ImageDetail', { imageId: image.id });
    console.log('Image pressed:', image.id)
  }

  const handleImageLongPress = (image: ImageItem) => {
    setSelectedImage(image)
    setShowDeleteModal(true)
  }

  const handleDeleteConfirm = async () => {
    if (!selectedImage) return

    try {
      await deleteImage(selectedImage.id)
      showSuccess('删除成功')
      setShowDeleteModal(false)
      setSelectedImage(null)
    } catch (error: any) {
      showError(error.message || '删除失败')
    }
  }

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyTitle}>暂无作品</Text>
      <Text style={styles.emptyMessage}>去首页选择风格生成你的第一张作品吧！</Text>
      <TouchableOpacity style={styles.emptyButton} onPress={() => navigation.navigate('Home')}>
        <Text style={styles.emptyButtonText}>去生成</Text>
      </TouchableOpacity>
    </View>
  )

  const renderLoginPrompt = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyTitle}>请先登录</Text>
      <Text style={styles.emptyMessage}>登录后即可查看您的创作作品</Text>
      <TouchableOpacity style={styles.emptyButton} onPress={() => navigation.navigate('Login')}>
        <Text style={styles.emptyButtonText}>去登录</Text>
      </TouchableOpacity>
    </View>
  )

  const renderFooter = () => {
    if (!pagination.hasMore) {
      return (
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>没有更多了</Text>
        </View>
      )
    }

    if (isLoading) {
      return (
        <View style={styles.footerContainer}>
          <Loading text='加载中...' />
        </View>
      )
    }

    return null
  }

  return (
    <View className='flex-1 bg-gray-900'>
      <StatusBar barStyle='light-content' backgroundColor='transparent' translucent />

      {/* 沉浸式顶部区域 */}
      <View className='pt-12 pb-4 px-5 bg-gradient-to-b from-gray-800/50 to-transparent'>
        <Text className='text-white text-xl font-bold text-center'>我的画廊</Text>
        <Text className='text-gray-400 text-sm text-center mt-1'>共 {pagination.total} 张作品</Text>
      </View>

      {!isLoggedIn ? (
        renderLoginPrompt()
      ) : images.length === 0 && !isLoading ? (
        renderEmptyState()
      ) : (
        <GalleryWaterfallGrid
          images={images}
          onImagePress={handleImagePress}
          onImageLongPress={handleImageLongPress}
          columnCount={2}
        />
      )}

      <ConfirmModal
        visible={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteConfirm}
        title='删除确认'
        message='确定要删除这张图片吗？删除后无法恢复。'
        confirmText='删除'
        confirmButtonVariant='outline'
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    paddingHorizontal: SPACING.LG,
    paddingVertical: SPACING.MD,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  title: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginTop: SPACING.XS,
  },

  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SPACING.LG,
    paddingTop: SPACING.XXL,
  },
  emptyTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  emptyMessage: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: SPACING.LG,
  },
  emptyButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SPACING.LG,
    paddingVertical: SPACING.SM,
    borderRadius: 8,
  },
  emptyButtonText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.BACKGROUND,
    fontWeight: '600',
  },
  footerContainer: {
    paddingVertical: SPACING.MD,
    alignItems: 'center',
  },
  footerText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
})
