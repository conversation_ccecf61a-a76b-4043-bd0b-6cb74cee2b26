import React, { useEffect, useState } from 'react'
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  StatusBar,
  Platform,
  Image,
} from 'react-native'
import { PackageService } from '@/services'
import { InAppPurchaseService } from '@/services/inAppPurchaseService'
import { MockInAppPurchaseService } from '@/services/mockInAppPurchaseService'
import { logo } from '@/assets'
import { useToast } from '@/hooks'
import { useAuthStore } from '@/store'
import { Loading } from '@/components/ui'
import type { PackageItem } from '@/types'

interface MembershipScreenProps {
  navigation: any
}

export const MembershipScreen: React.FC<MembershipScreenProps> = ({ navigation }) => {
  const [packages, setPackages] = useState<PackageItem[]>([])
  const [selectedPackage, setSelectedPackage] = useState<PackageItem | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isPurchasing, setIsPurchasing] = useState(false)
  const [activeTab, setActiveTab] = useState<'points' | 'vip'>('points') // 当前选中的标签

  const { showError, showSuccess } = useToast()
  const { isLoggedIn } = useAuthStore()

  useEffect(() => {
    loadPackages()
  }, [])

  useEffect(() => {
    // 当标签切换时，重新选择第一个套餐
    const filteredPackages = packages.filter(pkg => pkg.type === activeTab)
    if (filteredPackages.length > 0) {
      setSelectedPackage(filteredPackages[0])
    } else {
      setSelectedPackage(null)
    }
  }, [activeTab, packages])

  const loadPackages = async () => {
    try {
      setIsLoading(true)
      const data = await PackageService.getPackageList()
      setPackages(data)

      // 默认选择当前标签下的第一个套餐
      const filteredPackages = data.filter(pkg => pkg.type === activeTab)
      if (filteredPackages.length > 0) {
        setSelectedPackage(filteredPackages[0])
      }
    } catch (error: any) {
      showError(error.message || '加载套餐失败')
    } finally {
      setIsLoading(false)
    }
  }

  // 获取当前标签下的套餐
  const getFilteredPackages = () => {
    return packages.filter(pkg => pkg.type === activeTab)
  }

  // 切换标签时更新选中的套餐
  const handleTabChange = (tab: 'points' | 'vip') => {
    setActiveTab(tab)
    const filteredPackages = packages.filter(pkg => pkg.type === tab)
    if (filteredPackages.length > 0) {
      setSelectedPackage(filteredPackages[0])
    } else {
      setSelectedPackage(null)
    }
  }

  const handlePurchase = async () => {
    // 购买时需要登录
    if (!isLoggedIn) {
      Alert.alert('需要登录', '购买积分需要先登录账户', [
        { text: '取消', style: 'cancel' },
        { text: '去登录', onPress: () => navigation.navigate('Login') },
      ])
      return
    }

    if (!selectedPackage) {
      showError('请选择套餐')
      return
    }

    Alert.alert(
      '确认购买',
      `确定要购买 ${selectedPackage.name} 吗？\n价格：¥${selectedPackage.priceCNY}`,
      [
        { text: '取消', style: 'cancel' },
        { text: '确定', onPress: processPurchase },
      ]
    )
  }

  const processPurchase = async () => {
    if (!selectedPackage) return

    try {
      setIsPurchasing(true)

      if (Platform.OS === 'ios') {
        // iOS内购流程
        await handleIOSPurchase(selectedPackage)
      } else {
        // Android或其他平台的支付流程
        await handleWebPurchase(selectedPackage)
      }
    } catch (error: any) {
      showError(error.message || '购买失败')
    } finally {
      setIsPurchasing(false)
    }
  }

  const handleIOSPurchase = async (pkg: PackageItem) => {
    try {
      // 选择合适的内购服务
      const PurchaseService = MockInAppPurchaseService.isDevelopmentEnvironment()
        ? MockInAppPurchaseService
        : InAppPurchaseService

      console.log(
        `Using ${PurchaseService === MockInAppPurchaseService ? 'Mock' : 'Real'} InAppPurchase Service`
      )

      // 初始化内购服务
      const initialized = await PurchaseService.initialize()
      if (!initialized) {
        throw new Error('内购服务初始化失败')
      }

      // 获取产品ID
      const productId = PurchaseService.getProductIdFromPackage(pkg)

      // 执行购买
      const result = await PurchaseService.purchaseProduct(productId)

      if (result.success) {
        // 验证购买
        const verified = await PurchaseService.verifyPurchase(
          result.transactionId!,
          result.productId!
        )

        if (verified) {
          // 调用后端API更新用户积分/VIP状态
          if (PurchaseService === MockInAppPurchaseService) {
            // 开发环境模拟成功
            console.log('Mock purchase completed successfully')
            showSuccess('购买成功！积分已到账 (开发环境模拟)')
          } else {
            // 真实环境调用后端API
            await PackageService.processPurchase({
              packageId: pkg.id,
              transactionId: result.transactionId!,
              platform: 'ios',
            })
            showSuccess('购买成功！积分已到账')
          }

          navigation.goBack()
        } else {
          throw new Error('购买验证失败')
        }
      } else {
        if (result.error !== 'User cancelled purchase') {
          throw new Error(result.error || '购买失败')
        }
        // 用户取消购买，不显示错误
      }
    } catch (error: any) {
      console.error('iOS purchase failed:', error)
      throw error
    }
  }

  const handleWebPurchase = async (pkg: PackageItem) => {
    try {
      // 创建订单
      const orderResponse = await PackageService.createOrder({
        packageId: pkg.id,
        paymentMethod: 'wechat', // 这里可以让用户选择支付方式
      })

      // 这里应该调用支付SDK
      // 暂时模拟支付成功
      showSuccess('购买成功！')
      navigation.goBack()
    } catch (error) {
      throw error
    }
  }

  const renderPackageCard = (pkg: PackageItem) => {
    const isSelected = selectedPackage?.id === pkg.id
    const hasDiscount = pkg.originalPriceCNY > pkg.priceCNY

    return (
      <TouchableOpacity
        key={pkg.id}
        className={`relative bg-gray-800/50 rounded-2xl p-5 border-2 ${
          isSelected ? 'border-blue-500 bg-blue-500/10' : 'border-gray-700/30'
        }`}
        onPress={() => setSelectedPackage(pkg)}
      >
        {/* 热门标签 */}
        {pkg.isPopular && (
          <View className='absolute -top-2 -right-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-full px-3 py-1'>
            <Text className='text-white text-xs font-bold'>🔥 热门</Text>
          </View>
        )}

        {/* 选中标识 */}
        {isSelected && (
          <View className='absolute top-4 right-4 w-6 h-6 bg-blue-500 rounded-full items-center justify-center'>
            <Text className='text-white text-xs font-bold'>✓</Text>
          </View>
        )}

        {/* 套餐信息 */}
        <View className='mb-4'>
          <Text className='text-white text-lg font-bold mb-2'>{pkg.name}</Text>
          <View className='flex-row items-baseline'>
            <Text className='text-white text-2xl font-bold'>¥{pkg.priceCNY}</Text>
            {hasDiscount && (
              <Text className='text-gray-400 text-base line-through ml-2'>
                ¥{pkg.originalPriceCNY}
              </Text>
            )}
          </View>
        </View>

        {/* 套餐描述 */}
        <Text className='text-gray-300 text-sm mb-4 leading-5'>{pkg.description}</Text>

        {/* 套餐特性 */}
        <View className='space-y-2'>
          {pkg.type === 'points' ? (
            <View className='flex-row items-center'>
              <Text className='text-blue-400 text-sm mr-2'>💎</Text>
              <Text className='text-gray-300 text-sm'>{pkg.points} 次数</Text>
            </View>
          ) : (
            <View className='flex-row items-center'>
              <Text className='text-orange-400 text-sm mr-2'>⭐</Text>
              <Text className='text-gray-300 text-sm'>
                {pkg.vipDuration ? `${pkg.vipDuration} 天无限次数` : '永久无限次数'}
              </Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    )
  }

  if (isLoading) {
    return (
      <View className='flex-1 bg-gray-900'>
        <StatusBar barStyle='light-content' backgroundColor='transparent' translucent />
        <Loading text='加载套餐中...' />
      </View>
    )
  }

  return (
    <View className='flex-1 bg-gray-900'>
      <StatusBar barStyle='light-content' backgroundColor='transparent' translucent />

      {/* 导航栏 */}
      <View className='pt-12 pb-4 px-5 flex-row items-center'>
        <TouchableOpacity
          className='w-10 h-10 rounded-full bg-gray-800/50 items-center justify-center mr-4'
          onPress={() => navigation.goBack()}
        >
          <Text className='text-white text-lg'>←</Text>
        </TouchableOpacity>
        <Text className='text-white text-lg font-semibold'>选择套餐</Text>
      </View>

      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        {/* 头部介绍 */}
        <View className='px-5 py-6'>
          <View className='items-center mb-6'>
            <View className='w-20 h-20 mb-4 rounded-2xl overflow-hidden bg-white/10 items-center justify-center'>
              <Image source={logo} className='w-16 h-16' resizeMode='contain' />
            </View>
            <Text className='text-gray-400 text-base text-center'>
              享受更多创作乐趣，解锁无限可能
            </Text>
          </View>
        </View>

        {/* 标签切换 */}
        <View className='px-5 mb-6'>
          <View className='flex-row bg-gray-800/50 rounded-2xl p-1'>
            <TouchableOpacity
              className={`flex-1 py-3 rounded-xl ${
                activeTab === 'points' ? 'bg-blue-600' : 'bg-transparent'
              }`}
              onPress={() => handleTabChange('points')}
            >
              <Text
                className={`text-center font-semibold ${
                  activeTab === 'points' ? 'text-white' : 'text-gray-400'
                }`}
              >
                次数卡
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              className={`flex-1 py-3 rounded-xl ${
                activeTab === 'vip' ? 'bg-blue-600' : 'bg-transparent'
              }`}
              onPress={() => handleTabChange('vip')}
            >
              <Text
                className={`text-center font-semibold ${
                  activeTab === 'vip' ? 'text-white' : 'text-gray-400'
                }`}
              >
                无限次数会员
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 套餐列表 */}
        <View className='px-5 space-y-4'>{getFilteredPackages().map(renderPackageCard)}</View>

        {/* 权益说明 */}
        <View className='px-5 py-8'>
          <Text className='text-white text-lg font-bold mb-4'>
            {activeTab === 'points' ? '次数卡权益' : '会员权益'}
          </Text>
          <View className='bg-gray-800/50 rounded-2xl p-5 border border-gray-700/30'>
            <View className='space-y-3'>
              {activeTab === 'points' ? (
                <>
                  <View className='flex-row items-center'>
                    <Text className='text-blue-400 text-lg mr-3'>💎</Text>
                    <Text className='text-gray-300 text-base'>按次数使用，灵活消费</Text>
                  </View>
                  <View className='flex-row items-center'>
                    <Text className='text-green-400 text-lg mr-3'>🎨</Text>
                    <Text className='text-gray-300 text-base'>支持所有风格转换</Text>
                  </View>
                  <View className='flex-row items-center'>
                    <Text className='text-purple-400 text-lg mr-3'>📱</Text>
                    <Text className='text-gray-300 text-base'>高清图片下载</Text>
                  </View>
                  <View className='flex-row items-center'>
                    <Text className='text-orange-400 text-lg mr-3'>💰</Text>
                    <Text className='text-gray-300 text-base'>性价比高，适合轻度使用</Text>
                  </View>
                </>
              ) : (
                <>
                  <View className='flex-row items-center'>
                    <Text className='text-orange-400 text-lg mr-3'>⭐</Text>
                    <Text className='text-gray-300 text-base'>无限次数使用</Text>
                  </View>
                  <View className='flex-row items-center'>
                    <Text className='text-purple-400 text-lg mr-3'>🎨</Text>
                    <Text className='text-gray-300 text-base'>VIP专属风格</Text>
                  </View>
                  <View className='flex-row items-center'>
                    <Text className='text-green-400 text-lg mr-3'>⚡</Text>
                    <Text className='text-gray-300 text-base'>优先处理队列</Text>
                  </View>
                  <View className='flex-row items-center'>
                    <Text className='text-blue-400 text-lg mr-3'>📱</Text>
                    <Text className='text-gray-300 text-base'>高清图片下载</Text>
                  </View>
                  <View className='flex-row items-center'>
                    <Text className='text-red-400 text-lg mr-3'>🔥</Text>
                    <Text className='text-gray-300 text-base'>适合重度使用，更划算</Text>
                  </View>
                </>
              )}
            </View>
          </View>
        </View>
      </ScrollView>

      {/* 底部购买按钮 */}
      <View className='px-5 py-4 bg-gray-900 border-t border-gray-700/30'>
        <TouchableOpacity
          className={`py-4 rounded-2xl ${
            !selectedPackage || isPurchasing ? 'bg-gray-700' : 'bg-blue-600'
          }`}
          onPress={handlePurchase}
          disabled={!selectedPackage || isPurchasing}
        >
          <Text className='text-white text-base font-bold text-center'>
            {isPurchasing
              ? '购买中...'
              : selectedPackage?.type === 'vip'
                ? `开通会员 ¥${selectedPackage?.priceCNY || 0}`
                : `购买次数卡 ¥${selectedPackage?.priceCNY || 0}`}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}
