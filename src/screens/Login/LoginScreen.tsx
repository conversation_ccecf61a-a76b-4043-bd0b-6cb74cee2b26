import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
  TouchableOpacity,
  Image,
} from 'react-native'
import { useAuthStore } from '@/store'
import { useToast } from '@/hooks'
import { UserService } from '@/services'
import {
  isAppleSignInAvailable,
  signInWithApple,
  validatePhoneNumber,
  validateSmsCode,
} from '@/utils/authUtils'
import { APP_CONFIG } from '@/constants'
import { logo } from '@/assets'

interface LoginScreenProps {
  navigation: any
  route?: {
    params?: {
      inviteCode?: string
    }
  }
}

export const LoginScreen: React.FC<LoginScreenProps> = ({ navigation, route }) => {
  const [phone, setPhone] = useState('')
  const [smsCode, setSmsCode] = useState('')
  const [countdown, setCountdown] = useState(0)
  const [isAppleAvailable, setIsAppleAvailable] = useState(false)

  const { loginWithPhone, loginWithApple, isLoading } = useAuthStore()
  const { showError, showSuccess } = useToast()

  const inviteCode = route?.params?.inviteCode

  useEffect(() => {
    checkAppleSignInAvailability()
  }, [])

  useEffect(() => {
    let timer: NodeJS.Timeout
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000)
    }
    return () => clearTimeout(timer)
  }, [countdown])

  const checkAppleSignInAvailability = async () => {
    const available = await isAppleSignInAvailable()
    // 在开发环境中强制显示Apple登录选项（用于测试UI）
    const isDevelopment = __DEV__
    setIsAppleAvailable(available || isDevelopment)
  }

  const handleSendSmsCode = async () => {
    if (!validatePhoneNumber(phone)) {
      showError('请输入正确的手机号')
      return
    }

    try {
      await UserService.sendSmsCode(phone)
      setCountdown(60)
      showSuccess('验证码已发送')
    } catch (error: any) {
      showError(error.message || '发送验证码失败')
    }
  }

  const handlePhoneLogin = async () => {
    if (!validatePhoneNumber(phone)) {
      showError('请输入正确的手机号')
      return
    }

    if (!validateSmsCode(smsCode)) {
      showError('请输入6位验证码')
      return
    }

    try {
      await loginWithPhone(phone, smsCode, inviteCode)
      showSuccess('登录成功')
      navigation.replace('Main')
    } catch (error: any) {
      showError(error.message || '登录失败')
    }
  }

  const handleAppleLogin = async () => {
    try {
      // 在开发环境中提供模拟登录
      if (__DEV__ && Platform.OS !== 'ios') {
        showError('Apple登录仅在iOS真机上可用，开发环境中请使用手机号登录')
        return
      }

      const appleCredential = await signInWithApple()
      await loginWithApple(
        appleCredential.identityToken,
        appleCredential.authorizationCode,
        inviteCode
      )
      showSuccess('登录成功')
      navigation.replace('Main')
    } catch (error: any) {
      if (!error.message.includes('取消')) {
        showError(error.message || 'Apple登录失败')
      }
    }
  }

  const renderPhoneLogin = () => (
    <View className='space-y-6'>
      {/* 手机号输入 */}
      <View className='space-y-2'>
        <Text className='text-white text-base font-medium'>手机号</Text>
        <View className='bg-gray-800/50 rounded-2xl border border-gray-700/30'>
          <TextInput
            className='px-4 py-4 text-white text-base'
            placeholder='请输入手机号'
            placeholderTextColor='#9CA3AF'
            value={phone}
            onChangeText={setPhone}
            keyboardType='phone-pad'
            maxLength={11}
          />
        </View>
      </View>

      {/* 验证码输入 */}
      <View className='space-y-2'>
        <Text className='text-white text-base font-medium'>验证码</Text>
        <View className='flex-row space-x-3'>
          <View className='flex-1 bg-gray-800/50 rounded-2xl border border-gray-700/30'>
            <TextInput
              className='px-4 py-4 text-white text-base'
              placeholder='请输入验证码'
              placeholderTextColor='#9CA3AF'
              value={smsCode}
              onChangeText={setSmsCode}
              keyboardType='number-pad'
              maxLength={6}
            />
          </View>
          <TouchableOpacity
            className={`px-6 py-4 rounded-2xl border ${
              countdown > 0 || !validatePhoneNumber(phone)
                ? 'bg-gray-700/50 border-gray-600'
                : 'bg-blue-600 border-blue-500'
            }`}
            onPress={handleSendSmsCode}
            disabled={countdown > 0 || !validatePhoneNumber(phone)}
          >
            <Text
              className={`text-sm font-medium ${
                countdown > 0 || !validatePhoneNumber(phone) ? 'text-gray-400' : 'text-white'
              }`}
            >
              {countdown > 0 ? `${countdown}s` : '获取验证码'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* 登录按钮 */}
      <TouchableOpacity
        className={`py-4 rounded-2xl mt-8 ${
          !validatePhoneNumber(phone) || !validateSmsCode(smsCode) || isLoading
            ? 'bg-gray-700'
            : 'bg-blue-600'
        }`}
        onPress={handlePhoneLogin}
        disabled={!validatePhoneNumber(phone) || !validateSmsCode(smsCode) || isLoading}
      >
        <Text className='text-white text-base font-bold text-center'>
          {isLoading ? '登录中...' : '登录'}
        </Text>
      </TouchableOpacity>
    </View>
  )

  const renderAppleLogin = () => (
    <TouchableOpacity
      className='bg-black border border-gray-600 rounded-2xl py-4 flex-row items-center justify-center'
      onPress={handleAppleLogin}
      disabled={isLoading}
    >
      <Text className='text-white text-lg mr-2'>🍎</Text>
      <Text className='text-white text-base font-semibold'>
        {isLoading ? '登录中...' : '使用 Apple ID 登录'}
      </Text>
    </TouchableOpacity>
  )

  return (
    <View className='flex-1 bg-gray-900'>
      <StatusBar barStyle='light-content' backgroundColor='transparent' translucent />

      {/* 导航栏 */}
      <View className='pt-12 pb-4 px-5 flex-row items-center'>
        <TouchableOpacity
          className='w-10 h-10 rounded-full bg-gray-800/50 items-center justify-center mr-4'
          onPress={() => navigation.goBack()}
        >
          <Text className='text-white text-lg'>←</Text>
        </TouchableOpacity>
        <Text className='text-white text-lg font-semibold'>登录</Text>
      </View>

      <KeyboardAvoidingView
        className='flex-1'
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          className='flex-1 px-5'
          contentContainerStyle={{ flexGrow: 1 }}
          keyboardShouldPersistTaps='handled'
          showsVerticalScrollIndicator={false}
        >
          {/* 应用标题区域 */}
          <View className='items-center py-12'>
            <View className='w-24 h-24 mb-6 rounded-2xl overflow-hidden bg-white/10 items-center justify-center'>
              <Image source={logo} className='w-20 h-20' resizeMode='contain' />
            </View>
            <Text className='text-white text-2xl font-bold mb-2'>{APP_CONFIG.NAME}</Text>
            <Text className='text-gray-400 text-base'>让你的照片更有创意</Text>
          </View>

          {/* 登录表单区域 */}
          <View className='flex-1'>
            {renderPhoneLogin()}

            {isAppleAvailable && (
              <>
                <View className='flex-row items-center my-8'>
                  <View className='flex-1 h-px bg-gray-700' />
                  <Text className='text-gray-400 text-sm mx-4'>或</Text>
                  <View className='flex-1 h-px bg-gray-700' />
                </View>
                {renderAppleLogin()}
              </>
            )}

            {inviteCode && (
              <View className='mt-6 p-4 bg-blue-500/10 rounded-2xl border border-blue-500/20'>
                <Text className='text-blue-400 text-sm text-center font-medium'>
                  🎁 邀请码：{inviteCode}
                </Text>
              </View>
            )}
          </View>

          {/* 底部协议 */}
          <View className='py-8'>
            <Text className='text-gray-500 text-xs text-center leading-5'>
              登录即表示同意
              <Text className='text-blue-400'>《用户协议》</Text>和
              <Text className='text-blue-400'>《隐私政策》</Text>
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  )
}
