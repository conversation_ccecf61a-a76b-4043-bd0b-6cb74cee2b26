import React, { useEffect } from 'react'
import { View, Text, StatusBar, Image } from 'react-native'
import { useImageStore } from '@/store'
import { useToast, useAuthGuard } from '@/hooks'
import { Loading } from '@/components/ui'
import { StyleWaterfallGrid } from '@/components/common'
import { pickImage } from '@/utils/imageUtils'
import { APP_CONFIG } from '@/constants'
import { logo } from '@/assets'

interface HomeScreenProps {
  navigation: any
}

export const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const {
    styles,
    isLoading,
    isUploading,
    isGenerating,
    error,
    loadStyles,
    uploadImage,
    generateImage,
    clearError,
  } = useImageStore()

  const { showError, showSuccess, showConfirm } = useToast()
  const { requireAuth } = useAuthGuard()

  useEffect(() => {
    loadStyles()
  }, [])

  useEffect(() => {
    if (error) {
      showError(error)
      clearError()
    }
  }, [error])

  const handleStyleSelect = (style: any) => {
    requireAuth(() => handleImagePicker(style.id), {
      requireLogin: true,
      requirePoints: true,
      onUnauthorized: (reason: string, action?: any) => {
        if (action === 'login') {
          navigation.navigate('Login')
        } else if (action === 'purchase') {
          navigation.navigate('Membership')
        } else {
          showError(reason)
        }
      },
    })
  }

  const handleImagePicker = async (styleId: number) => {
    try {
      const selectedImage = await pickImage()
      if (!selectedImage) return

      showConfirm('确认生成', '确定要使用此风格生成图片吗？这将消耗1个积分。', async () => {
        try {
          // 上传图片
          const imageId = await uploadImage(selectedImage.uri)

          // 生成图片
          await generateImage(imageId, styleId)

          showSuccess('图片生成成功！')

          // 跳转到画廊查看结果
          navigation.navigate('Gallery')
        } catch (error: any) {
          showError(error.message || '生成失败')
        }
      })
    } catch (error: any) {
      showError(error.message || '选择图片失败')
    }
  }

  if (isLoading) {
    return (
      <View className='flex-1 bg-gray-900'>
        <StatusBar barStyle='light-content' backgroundColor='transparent' translucent />
        <Loading text='加载风格中...' />
      </View>
    )
  }

  return (
    <View className='flex-1 bg-gray-900'>
      <StatusBar barStyle='light-content' backgroundColor='transparent' translucent />

      {/* 沉浸式顶部区域 */}
      <View className='pt-16 pb-4 px-5 bg-gradient-to-b from-gray-800/50 to-transparent'>
        <View className='flex-row items-center justify-between'>
          <View className='flex-row items-center'>
            <View className='w-8 h-8 mr-3 rounded-lg overflow-hidden bg-white/10 items-center justify-center'>
              <Image source={logo} className='w-6 h-6' resizeMode='contain' />
            </View>
            <Text className='text-white text-xl font-bold'>{APP_CONFIG.NAME}</Text>
          </View>
        </View>
      </View>

      <StyleWaterfallGrid styles={styles} onStylePress={handleStyleSelect} columnCount={2} />

      {(isUploading || isGenerating) && (
        <Loading text={isUploading ? '上传中...' : '生成中...'} overlay />
      )}
    </View>
  )
}
