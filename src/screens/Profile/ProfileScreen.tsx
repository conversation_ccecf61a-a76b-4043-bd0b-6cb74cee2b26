import React, { useState } from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  TextInput,
  Modal,
  Dimensions,
  StatusBar,
} from 'react-native'
import { useAuthStore } from '@/store'
import { useToast } from '@/hooks'
import { formatUserDisplayName, isVipUser, formatVipExpireTime } from '@/utils/authUtils'
import { DEFAULT_AVATAR } from '@/constants'
import { logo } from '@/assets'

const { width: screenWidth } = Dimensions.get('window')

interface ProfileScreenProps {
  navigation: any
}

export const ProfileScreen: React.FC<ProfileScreenProps> = ({ navigation }) => {
  const { user, logout, isLoggedIn } = useAuthStore()
  const { showSuccess, showConfirm, showError } = useToast()
  const [couponModalVisible, setCouponModalVisible] = useState(false)
  const [couponCode, setCouponCode] = useState('')
  const [submitting, setSubmitting] = useState(false)

  const handleLogout = () => {
    showConfirm('退出登录', '确定要退出登录吗？', async () => {
      try {
        await logout()
        showSuccess('已退出登录')
      } catch (error: any) {
        console.error('Logout error:', error)
        // 即使出错也要退出
      }
    })
  }

  const handleMembershipPress = () => {
    navigation.navigate('Membership')
  }

  const handleSettingsPress = () => {
    Alert.alert('提示', '设置功能开发中...')
  }

  const handleAboutPress = () => {
    Alert.alert('关于', '吉卜力风格AI v1.0.0\n让你的照片更有创意')
  }

  const renderUserHeader = () => {
    const displayName = isLoggedIn && user ? formatUserDisplayName(user) : '未登录'
    const isVip = isLoggedIn && user ? isVipUser(user) : false
    const vipExpireText = isLoggedIn && user ? formatVipExpireTime(user.vipExpireAt) : ''
    const points = isLoggedIn && user ? user.points || 0 : 0

    return (
      <View className='px-5 pt-12 pb-8'>
        {/* 用户基本信息 */}
        <View className='flex-row items-center mb-6'>
          <View className='relative w-16 h-16 mr-4 rounded-full bg-cyan-400/20 justify-center items-center'>
            <Image
              source={{ uri: (isLoggedIn && user?.avatarUrl) || DEFAULT_AVATAR }}
              className='w-14 h-14 rounded-full'
            />
            {/* 头像装饰圆环 */}
            <View className='absolute inset-0 rounded-full border-2 border-cyan-400/30' />
            {/* Logo装饰 */}
            <View className='absolute -bottom-1 -right-1 w-6 h-6 bg-white rounded-full items-center justify-center border-2 border-gray-900'>
              <Image source={logo} className='w-4 h-4' resizeMode='contain' />
            </View>
          </View>
          <View className='flex-1'>
            <Text className='text-white text-xl font-bold mb-1'>{displayName}</Text>
            <View className='flex-row items-center justify-between'>
              <View className='flex-row items-center'>
                <Text className='text-gray-400 text-sm mr-2'>用户ID:</Text>
                <Text className='text-gray-300 text-sm'>******</Text>
              </View>
              <View className='flex-row items-center bg-orange-100/10 rounded-full px-3 py-1'>
                <Text className='text-orange-400 text-sm mr-1'>🎨</Text>
                <Text className='text-orange-400 text-sm font-bold'>{points}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* VIP卡片 */}
        <View className='bg-gradient-to-r from-orange-100 to-orange-50 rounded-2xl p-5 flex-row items-center justify-between shadow-lg'>
          <View className='flex-1'>
            <View className='flex-row items-center mb-2'>
              <Text className='text-orange-800 text-lg font-bold mr-2'>吉卜力风格AI</Text>
              <View className='bg-orange-200 rounded-full px-2 py-1'>
                <Text className='text-orange-800 text-xs font-bold'>💎 VIP</Text>
              </View>
            </View>
            <Text className='text-orange-700 text-sm leading-5'>
              {isVip ? `到期时间: ${vipExpireText}` : '成为VIP即可解锁更多会员权益。'}
            </Text>
          </View>
          <TouchableOpacity
            className='bg-gray-800 rounded-full px-6 py-3 shadow-md'
            onPress={handleMembershipPress}
          >
            <Text className='text-white text-sm font-bold'>{isVip ? '续费' : '立即开通'}</Text>
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  const handleCouponSubmit = async () => {
    if (!couponCode.trim()) {
      showError('请输入优惠券码')
      return
    }

    setSubmitting(true)
    try {
      // 这里调用兑换优惠券的API
      // await UserService.redeemCoupon(couponCode)
      showSuccess('优惠券兑换成功！')
      setCouponCode('')
      setCouponModalVisible(false)
    } catch (error: any) {
      showError(error.message || '兑换失败')
    } finally {
      setSubmitting(false)
    }
  }

  const renderCouponModal = () => (
    <Modal visible={couponModalVisible} transparent animationType='fade'>
      <View style={styles.overlay}>
        <View style={styles.modal}>
          <Text style={styles.modalTitle}>兑换优惠券</Text>
          <View style={styles.modalContent}>
            <TextInput
              style={styles.input}
              placeholder='请输入优惠券码'
              placeholderTextColor='#a3a3a3'
              value={couponCode}
              onChangeText={setCouponCode}
              autoCapitalize='characters'
            />
          </View>
          <View style={styles.modalFooter}>
            <TouchableOpacity
              style={styles.cancelBtn}
              onPress={() => {
                setCouponModalVisible(false)
                setCouponCode('')
              }}
            >
              <Text style={styles.cancelBtnText}>取消</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.confirmBtn, submitting && styles.disabledBtn]}
              onPress={handleCouponSubmit}
              disabled={submitting}
            >
              <Text style={styles.confirmBtnText}>{submitting ? '兑换中...' : '确认'}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  )

  const renderFunctionList = () => (
    <View className='px-5 py-4'>
      <View className='space-y-3'>
        {/* 我的空间 */}
        <TouchableOpacity
          className='bg-gray-800/50 rounded-2xl p-4 flex-row items-center justify-between border border-gray-700/30'
          onPress={() => navigation.navigate('Gallery')}
        >
          <View className='flex-row items-center'>
            <View className='w-8 h-8 bg-blue-500/20 rounded-lg justify-center items-center mr-3'>
              <Text className='text-blue-400 text-lg'>📦</Text>
            </View>
            <Text className='text-white text-base font-medium'>我的空间</Text>
          </View>
          <Text className='text-gray-400 text-xl'>›</Text>
        </TouchableOpacity>

        {/* 我的收藏 */}
        <TouchableOpacity
          className='bg-gray-800/50 rounded-2xl p-4 flex-row items-center justify-between border border-gray-700/30'
          onPress={() => navigation.navigate('Gallery')}
        >
          <View className='flex-row items-center'>
            <View className='w-8 h-8 bg-yellow-500/20 rounded-lg justify-center items-center mr-3'>
              <Text className='text-yellow-400 text-lg'>⭐</Text>
            </View>
            <Text className='text-white text-base font-medium'>我的收藏</Text>
          </View>
          <Text className='text-gray-400 text-xl'>›</Text>
        </TouchableOpacity>

        {/* 帮助与反馈 */}
        <TouchableOpacity
          className='bg-gray-800/50 rounded-2xl p-4 flex-row items-center justify-between border border-gray-700/30'
          onPress={handleAboutPress}
        >
          <View className='flex-row items-center'>
            <View className='w-8 h-8 bg-white/20 rounded-lg justify-center items-center mr-3'>
              <Text className='text-white text-lg'>📄</Text>
            </View>
            <Text className='text-white text-base font-medium'>帮助与反馈</Text>
          </View>
          <Text className='text-gray-400 text-xl'>›</Text>
        </TouchableOpacity>

        {/* 自动消耗 */}
        <TouchableOpacity
          className='bg-gray-800/50 rounded-2xl p-4 flex-row items-center justify-between border border-gray-700/30'
          onPress={handleSettingsPress}
        >
          <View className='flex-row items-center'>
            <View className='w-8 h-8 bg-white/20 rounded-lg justify-center items-center mr-3'>
              <Text className='text-white text-lg'>⚙️</Text>
            </View>
            <Text className='text-white text-base font-medium'>自动消耗</Text>
          </View>
          <Text className='text-gray-400 text-xl'>›</Text>
        </TouchableOpacity>

        {/* 邀请有礼 */}
        <TouchableOpacity
          className='bg-gray-800/50 rounded-2xl p-4 flex-row items-center justify-between border border-gray-700/30'
          onPress={() => setCouponModalVisible(true)}
        >
          <View className='flex-row items-center'>
            <View className='w-8 h-8 bg-white/20 rounded-lg justify-center items-center mr-3'>
              <Text className='text-white text-lg'>🎁</Text>
            </View>
            <Text className='text-white text-base font-medium'>邀请有礼</Text>
          </View>
          <Text className='text-gray-400 text-xl'>›</Text>
        </TouchableOpacity>
      </View>
    </View>
  )

  return (
    <View className='flex-1 bg-gray-900'>
      <StatusBar barStyle='light-content' backgroundColor='transparent' translucent />
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        {renderUserHeader()}
        {renderFunctionList()}

        <View className='px-5 py-8 mt-4'>
          {isLoggedIn ? (
            <TouchableOpacity
              className='bg-gray-800/50 border border-gray-600 rounded-2xl py-4'
              onPress={handleLogout}
            >
              <Text className='text-gray-300 text-center text-base font-medium'>退出登录</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              className='bg-blue-600 rounded-2xl py-4'
              onPress={() => navigation.navigate('Login')}
            >
              <Text className='text-white text-center text-base font-bold'>登录</Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>
      {renderCouponModal()}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0a0e17',
    // 添加渐变背景效果
  },
  scrollView: {
    flex: 1,
  },
  // 用户头部区域
  userHeader: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 30,
  },
  userBasic: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 28,
  },
  avatarWrapper: {
    position: 'relative',
    width: 55,
    height: 55,
    marginRight: 16,
    borderRadius: 27.5,
    backgroundColor: 'rgba(15, 224, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#0fe0ff',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 5,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: '#0fe0ff',
  },
  avatarOverlay: {
    position: 'absolute',
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(15, 224, 255, 0.2)',
    // mixBlendMode: 'color-dodge', // React Native doesn't support this
  },
  nameContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  nickname: {
    fontSize: 16,
    fontWeight: '600',
    color: '#e6e6e6',
    letterSpacing: 0.5,
    textShadowColor: 'rgba(15, 224, 255, 0.5)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 5,
  },
  point: {
    fontSize: 16,
    color: '#e6e6e6',
    letterSpacing: 0.5,
    textShadowColor: 'rgba(15, 224, 255, 0.5)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 5,
  },
  vipExpire: {
    fontSize: 16,
    color: '#e6e6e6',
    letterSpacing: 0.5,
    textShadowColor: 'rgba(230, 230, 230, 0.3)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 5,
  },
  // 会员卡片样式
  vipCard: {
    backgroundColor: '#171c26',
    borderRadius: 12,
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: '#2a2f3a',
    borderTopWidth: 2,
    borderTopColor: '#0fe0ff',
  },
  vipInfo: {
    flex: 1,
  },
  vipTitle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  vipIcon: {
    width: 24,
    height: 24,
    lineHeight: 24,
    textAlign: 'center',
    backgroundColor: '#0fe0ff',
    borderRadius: 12,
    color: '#0a0e17',
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
    shadowColor: '#0fe0ff',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 5,
    elevation: 3,
  },
  vipText: {
    color: '#0fe0ff',
    fontSize: 18,
    fontWeight: '600',
    letterSpacing: 0.5,
    textShadowColor: 'rgba(15, 224, 255, 0.5)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 5,
  },
  vipDesc: {
    color: '#a3a3a3',
    fontSize: 14,
    letterSpacing: 0.25,
  },
  vipButton: {
    backgroundColor: '#0fe0ff',
    borderRadius: 10,
    paddingHorizontal: 20,
    paddingVertical: 8,
    shadowColor: '#0fe0ff',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 7.5,
    elevation: 5,
  },
  vipAction: {
    fontSize: 16,
    color: '#0a0e17',
    fontWeight: '500',
  },
  // 功能列表样式
  functionList: {
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: '#e6e6e6',
    marginBottom: 16,
    letterSpacing: 0.5,
    textShadowColor: 'rgba(15, 224, 255, 0.3)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 5,
  },
  menuList: {
    backgroundColor: '#171c26',
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#2a2f3a',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.4,
    shadowRadius: 8,
    elevation: 8,
    borderTopWidth: 2,
    borderTopColor: '#0fe0ff',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 18,
    paddingVertical: 18,
    backgroundColor: '#171c26',
    borderBottomWidth: 1,
    borderBottomColor: '#2a2f3a',
  },
  menuLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuIcon: {
    marginRight: 12,
    fontSize: 20,
  },
  menuText: {
    fontSize: 16,
    color: '#e6e6e6',
    fontWeight: '500',
  },
  menuTip: {
    fontSize: 16,
    color: '#e6e6e6',
  },
  menuArrow: {
    color: '#a3a3a3',
    fontSize: 20,
  },
  // 模态框样式
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(10, 14, 23, 0.85)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modal: {
    backgroundColor: '#171c26',
    borderRadius: 10,
    width: screenWidth * 0.85,
    padding: 30,
    shadowColor: '#0fe0ff',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.18,
    shadowRadius: 10,
    elevation: 10,
  },
  modalTitle: {
    fontSize: 17,
    fontWeight: 'bold',
    color: '#0fe0ff',
    textAlign: 'center',
    marginBottom: 10,
    letterSpacing: 1,
    textShadowColor: 'rgba(15, 224, 255, 0.3)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 6,
  },
  modalContent: {
    marginBottom: 16,
    alignItems: 'center',
  },
  input: {
    width: '100%',
    borderWidth: 2,
    borderColor: '#0fe0ff',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 8,
    fontSize: 15,
    backgroundColor: '#232b3a',
    color: '#e6e6e6',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 24,
  },
  confirmBtn: {
    flex: 1,
    backgroundColor: '#0fe0ff',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    shadowColor: '#0fe0ff',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 3,
  },
  confirmBtnText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0a0e17',
  },
  cancelBtn: {
    flex: 1,
    backgroundColor: '#232b3a',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#2a2f3a',
  },
  cancelBtnText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#a3a3a3',
  },
  disabledBtn: {
    opacity: 0.6,
  },
  logoutContainer: {
    margin: 20,
    marginTop: 30,
  },
})
