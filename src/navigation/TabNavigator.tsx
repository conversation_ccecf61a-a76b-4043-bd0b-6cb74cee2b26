import React from 'react'
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs'
import { Platform } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import Icon from 'react-native-vector-icons/Ionicons'
import { HomeScreen } from '../screens/Home/HomeScreen'
import { GalleryScreen } from '../screens/Gallery/GalleryScreen'
import { ProfileScreen } from '../screens/Profile/ProfileScreen'
import { COLORS, FONT_SIZES } from '../constants'
import type { TabParamList } from '../types'

const Tab = createBottomTabNavigator<TabParamList>()

export const TabNavigator: React.FC = () => {
  const insets = useSafeAreaInsets()

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string

          switch (route.name) {
            case 'Home':
              iconName = focused ? 'home' : 'home-outline'
              break
            case 'Gallery':
              iconName = focused ? 'images' : 'images-outline'
              break
            case 'Profile':
              iconName = focused ? 'person' : 'person-outline'
              break
            default:
              iconName = 'help-outline'
          }

          return <Icon name={iconName} size={size} color={color} />
        },
        tabBarActiveTintColor: COLORS.PRIMARY,
        tabBarInactiveTintColor: COLORS.TEXT_SECONDARY,
        tabBarStyle: {
          backgroundColor: COLORS.BACKGROUND,
          borderTopColor: COLORS.BORDER,
          borderTopWidth: 1,
          paddingTop: 8,
          paddingBottom: Platform.OS === 'ios' ? Math.max(insets.bottom, 8) : 8,
          height: Platform.OS === 'ios' ? 60 + Math.max(insets.bottom - 8, 0) : 60,
        },
        tabBarLabelStyle: {
          fontSize: FONT_SIZES.XS,
          fontWeight: '500',
        },
        headerShown: false,
      })}
    >
      <Tab.Screen
        name='Home'
        component={HomeScreen}
        options={{
          tabBarLabel: '首页',
        }}
      />
      <Tab.Screen
        name='Gallery'
        component={GalleryScreen}
        options={{
          tabBarLabel: '画廊',
        }}
      />
      <Tab.Screen
        name='Profile'
        component={ProfileScreen}
        options={{
          tabBarLabel: '我的',
        }}
      />
    </Tab.Navigator>
  )
}
