import React, { useEffect, useState } from 'react'
import { NavigationContainer } from '@react-navigation/native'
import { createStackNavigator } from '@react-navigation/stack'
import { useInitializeApp } from '../store'
import { LoginScreen } from '../screens/Login/LoginScreen'
import { MembershipScreen } from '../screens'
import { TabNavigator } from './TabNavigator'
import { FullScreenLoading } from '../components/ui'
import type { RootStackParamList } from '../types'

const Stack = createStackNavigator<RootStackParamList>()

export const RootNavigator: React.FC = () => {
  const [isInitializing, setIsInitializing] = useState(true)

  const { initialize } = useInitializeApp()

  useEffect(() => {
    const initApp = async () => {
      try {
        await initialize()
      } catch (error) {
        console.error('App initialization failed:', error)
      } finally {
        setIsInitializing(false)
      }
    }

    initApp()
  }, [])

  if (isInitializing) {
    return <FullScreenLoading text='初始化中...' />
  }

  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: true,
        }}
        initialRouteName='Main'
      >
        {/* 主要页面 - 所有用户都可以访问 */}
        <Stack.Screen name='Main' component={TabNavigator} />

        {/* 登录页面 - 全屏展示 */}
        <Stack.Screen
          name='Login'
          component={LoginScreen}
          options={{
            presentation: 'card',
            headerShown: false,
          }}
        />

        {/* 会员中心 - 全屏展示 */}
        <Stack.Screen
          name='Membership'
          component={MembershipScreen}
          options={{
            headerShown: false,
            presentation: 'card',
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  )
}
