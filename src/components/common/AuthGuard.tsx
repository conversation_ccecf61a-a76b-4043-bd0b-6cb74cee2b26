import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import { useAuthStore } from '../../store'
import { Button } from '../ui'
import { checkUserAccess, AccessAction } from '../../utils/authUtils'
import { COLORS, FONT_SIZES, SPACING } from '../../constants'

interface AuthGuardProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  requireLogin?: boolean
  requirePoints?: boolean
  onLoginRequired?: () => void
  onPointsRequired?: () => void
}

export const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  fallback,
  requireLogin = true,
  requirePoints = false,
  onLoginRequired,
  onPointsRequired,
}) => {
  const { isLoggedIn, user } = useAuthStore()

  // 检查登录状态
  if (requireLogin && !isLoggedIn) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <View style={styles.container}>
        <Text style={styles.title}>需要登录</Text>
        <Text style={styles.message}>请先登录以使用此功能</Text>
        <Button title='去登录' onPress={onLoginRequired || (() => {})} style={styles.button} />
      </View>
    )
  }

  // 检查积分权限
  if (requirePoints && user) {
    const accessResult = checkUserAccess(user)

    if (!accessResult.canUse && accessResult.action === AccessAction.PURCHASE) {
      if (fallback) {
        return <>{fallback}</>
      }

      return (
        <View style={styles.container}>
          <Text style={styles.title}>积分不足</Text>
          <Text style={styles.message}>
            {accessResult.reason || '当前积分不足，请购买积分后继续使用'}
          </Text>
          <Button title='购买积分' onPress={onPointsRequired || (() => {})} style={styles.button} />
        </View>
      )
    }
  }

  return <>{children}</>
}

// 高阶组件版本
export function withAuthGuard<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    requireLogin?: boolean
    requirePoints?: boolean
    fallback?: React.ReactNode
  } = {}
) {
  const { requireLogin = true, requirePoints = false, fallback } = options

  return function AuthGuardedComponent(props: P) {
    return (
      <AuthGuard requireLogin={requireLogin} requirePoints={requirePoints} fallback={fallback}>
        <Component {...props} />
      </AuthGuard>
    )
  }
}

// Hook版本
export const useAuthGuard = () => {
  const { isLoggedIn, user } = useAuthStore()

  const checkPermission = (
    options: {
      requireLogin?: boolean
      requirePoints?: boolean
    } = {}
  ) => {
    const { requireLogin = true, requirePoints = false } = options

    if (requireLogin && !isLoggedIn) {
      return {
        allowed: false,
        reason: '需要登录',
        action: AccessAction.LOGIN,
      }
    }

    if (requirePoints && user) {
      const accessResult = checkUserAccess(user)
      if (!accessResult.canUse) {
        return {
          allowed: false,
          reason: accessResult.reason,
          action: accessResult.action,
        }
      }
    }

    return {
      allowed: true,
    }
  }

  const requireAuth = (
    callback: () => void,
    options?: {
      requireLogin?: boolean
      requirePoints?: boolean
      onUnauthorized?: (reason: string, action?: AccessAction) => void
    }
  ) => {
    const permission = checkPermission(options)

    if (permission.allowed) {
      callback()
    } else if (options?.onUnauthorized) {
      options.onUnauthorized(permission.reason || '', permission.action)
    }
  }

  return {
    isLoggedIn,
    user,
    checkPermission,
    requireAuth,
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.LG,
    backgroundColor: COLORS.BACKGROUND,
  },
  title: {
    fontSize: FONT_SIZES.XL,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  message: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: SPACING.LG,
  },
  button: {
    minWidth: 120,
  },
})
