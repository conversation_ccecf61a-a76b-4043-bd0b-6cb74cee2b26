import React from 'react'
import {
  View,
  ScrollView,
  TouchableOpacity,
  Image,
  Text,
  StyleSheet,
  ViewStyle,
} from 'react-native'
import { useWaterfall } from '@/hooks'
import { COLORS, FONT_SIZES, SPACING } from '@/constants'
import type { ImageItem, Style } from '@/types'

interface WaterfallGridProps {
  data: (ImageItem | Style)[]
  imgKey: 'preview' | 'resultUrl' | 'resultUrlPreview'
  columnCount?: number
  gutter?: number
  onItemPress?: (item: ImageItem | Style, index: number) => void
  onItemLongPress?: (item: ImageItem | Style, index: number) => void
  renderOverlay?: (item: ImageItem | Style) => React.ReactNode
  style?: ViewStyle
  contentContainerStyle?: ViewStyle
}

export const WaterfallGrid: React.FC<WaterfallGridProps> = ({
  data,
  imgKey,
  columnCount = 2,
  gutter = 12,
  onItemPress,
  onItemLongPress,
  renderOverlay,
  style,
  contentContainerStyle,
}) => {
  const { columns, columnWidth } = useWaterfall({
    imageList: data,
    imgKey,
    columnCount,
    gutter,
  })

  const renderItem = (item: any, columnIndex: number, itemIndex: number) => {
    const globalIndex = columnIndex * Math.ceil(data.length / columnCount) + itemIndex

    return (
      <TouchableOpacity
        key={item.id}
        style={[
          waterfallStyles.item,
          {
            width: columnWidth,
            marginBottom: gutter,
          },
        ]}
        onPress={() => onItemPress?.(item, globalIndex)}
        onLongPress={() => onItemLongPress?.(item, globalIndex)}
        activeOpacity={0.8}
      >
        <Image
          source={{ uri: item.url || item.preview }}
          style={[waterfallStyles.image, { height: item.height }]}
          resizeMode='cover'
        />

        {/* 默认覆盖层 */}
        <View style={waterfallStyles.defaultOverlay}>
          <Text style={waterfallStyles.itemName} numberOfLines={2}>
            {item.name}
          </Text>
        </View>

        {/* 自定义覆盖层 */}
        {renderOverlay?.(item)}
      </TouchableOpacity>
    )
  }

  return (
    <ScrollView
      style={[waterfallStyles.container, style]}
      contentContainerStyle={[waterfallStyles.contentContainer, contentContainerStyle]}
      showsVerticalScrollIndicator={false}
    >
      <View style={[waterfallStyles.grid, { gap: gutter }]}>
        {columns.map((column, columnIndex) => (
          <View key={columnIndex} style={[waterfallStyles.column, { width: columnWidth }]}>
            {column.map((item, itemIndex) => renderItem(item, columnIndex, itemIndex))}
          </View>
        ))}
      </View>
    </ScrollView>
  )
}

// 专门用于风格展示的瀑布流组件
export const StyleWaterfallGrid: React.FC<{
  styles: Style[]
  onStylePress: (style: Style) => void
  columnCount?: number
}> = ({ styles, onStylePress, columnCount = 2 }) => {
  return (
    <WaterfallGrid
      data={styles}
      imgKey='preview'
      columnCount={columnCount}
      onItemPress={item => onStylePress(item as Style)}
      renderOverlay={item => {
        const style = item as Style
        return (
          <>
            {style.isPopular && (
              <View style={waterfallStyles.popularBadge}>
                <Text style={waterfallStyles.popularText}>热门</Text>
              </View>
            )}
          </>
        )
      }}
    />
  )
}

// 专门用于图片画廊的瀑布流组件
export const GalleryWaterfallGrid: React.FC<{
  images: ImageItem[]
  onImagePress: (image: ImageItem) => void
  onImageLongPress?: (image: ImageItem) => void
  columnCount?: number
}> = ({ images, onImagePress, onImageLongPress, columnCount = 2 }) => {
  return (
    <WaterfallGrid
      data={images}
      imgKey='resultUrlPreview'
      columnCount={columnCount}
      onItemPress={item => onImagePress(item as ImageItem)}
      onItemLongPress={onImageLongPress ? item => onImageLongPress(item as ImageItem) : undefined}
      renderOverlay={item => {
        const image = item as ImageItem
        const isCompleted = image.status === 'completed'

        if (!isCompleted) {
          return (
            <View style={waterfallStyles.statusOverlay}>
              <Text style={waterfallStyles.statusText}>
                {image.status === 'pending' && '等待处理'}
                {image.status === 'processing' && '处理中'}
                {image.status === 'failed' && '处理失败'}
              </Text>
            </View>
          )
        }
        return null
      }}
    />
  )
}

const waterfallStyles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: SPACING.MD,
  },
  grid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  column: {
    flex: 1,
  },
  item: {
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: COLORS.SURFACE,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  image: {
    width: '100%',
  },
  defaultOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: SPACING.SM,
  },
  itemName: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
  },
  popularBadge: {
    position: 'absolute',
    top: SPACING.SM,
    right: SPACING.SM,
    backgroundColor: COLORS.ERROR,
    paddingHorizontal: SPACING.SM,
    paddingVertical: 2,
    borderRadius: 8,
  },
  popularText: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '600',
  },
  statusOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  statusText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
  },
})
