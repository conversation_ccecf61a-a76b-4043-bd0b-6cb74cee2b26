import React from 'react'
import { View, ScrollView, TouchableOpacity, Image, Text, ViewStyle } from 'react-native'
import { useWaterfall } from '@/hooks'
import type { ImageItem, Style } from '@/types'

interface WaterfallGridProps {
  data: (ImageItem | Style)[]
  imgKey: 'preview' | 'resultUrl' | 'resultUrlPreview'
  columnCount?: number
  gutter?: number
  onItemPress?: (item: ImageItem | Style, index: number) => void
  onItemLongPress?: (item: ImageItem | Style, index: number) => void
  renderOverlay?: (item: ImageItem | Style) => React.ReactNode
  style?: ViewStyle
  contentContainerStyle?: ViewStyle
}

export const WaterfallGrid: React.FC<WaterfallGridProps> = ({
  data,
  imgKey,
  columnCount = 2,
  gutter = 12,
  onItemPress,
  onItemLongPress,
  renderOverlay,
  style,
  contentContainerStyle,
}) => {
  const { columns, columnWidth } = useWaterfall({
    imageList: data,
    imgKey,
    columnCount,
    gutter,
  })

  const renderItem = (item: any, columnIndex: number, itemIndex: number) => {
    const globalIndex = columnIndex * Math.ceil(data.length / columnCount) + itemIndex

    return (
      <TouchableOpacity
        key={item.id}
        className='relative rounded-xl overflow-hidden bg-gray-800 shadow-lg'
        style={{
          width: columnWidth,
          marginBottom: gutter,
        }}
        onPress={() => onItemPress?.(item, globalIndex)}
        onLongPress={() => onItemLongPress?.(item, globalIndex)}
        activeOpacity={0.8}
      >
        <Image
          source={{ uri: item.url || item.preview }}
          className='w-full'
          style={{ height: item.height }}
          resizeMode='cover'
        />

        {/* 默认覆盖层 */}
        <View className='absolute bottom-0 left-0 right-0 bg-black/60 p-1'>
          <Text className='text-white text-xs font-medium text-center' numberOfLines={1}>
            {item.name}
          </Text>
        </View>

        {/* 自定义覆盖层 */}
        {renderOverlay?.(item)}
      </TouchableOpacity>
    )
  }

  return (
    <ScrollView
      className='flex-1'
      contentContainerStyle={[{ padding: 16 }, contentContainerStyle]}
      style={style}
      showsVerticalScrollIndicator={false}
    >
      <View className='flex-row justify-between' style={{ gap: gutter }}>
        {columns.map((column, columnIndex) => (
          <View key={columnIndex} className='flex-1' style={{ width: columnWidth }}>
            {column.map((item, itemIndex) => renderItem(item, columnIndex, itemIndex))}
          </View>
        ))}
      </View>
    </ScrollView>
  )
}

// 专门用于风格展示的瀑布流组件
export const StyleWaterfallGrid: React.FC<{
  styles: Style[]
  onStylePress: (style: Style) => void
  columnCount?: number
}> = ({ styles, onStylePress, columnCount = 2 }) => {
  return (
    <WaterfallGrid
      data={styles}
      imgKey='preview'
      columnCount={columnCount}
      onItemPress={item => onStylePress(item as Style)}
      renderOverlay={item => {
        const style = item as Style
        return (
          <>
            {style.isPopular && (
              <View className='absolute top-3 right-3 bg-red-500 px-2 py-1 rounded-lg'>
                <Text className='text-white text-xs font-semibold'>热门</Text>
              </View>
            )}
          </>
        )
      }}
    />
  )
}

// 专门用于图片画廊的瀑布流组件
export const GalleryWaterfallGrid: React.FC<{
  images: ImageItem[]
  onImagePress: (image: ImageItem) => void
  onImageLongPress?: (image: ImageItem) => void
  columnCount?: number
}> = ({ images, onImagePress, onImageLongPress, columnCount = 2 }) => {
  return (
    <WaterfallGrid
      data={images}
      imgKey='resultUrlPreview'
      columnCount={columnCount}
      onItemPress={item => onImagePress(item as ImageItem)}
      onItemLongPress={onImageLongPress ? item => onImageLongPress(item as ImageItem) : undefined}
      renderOverlay={item => {
        const image = item as ImageItem
        const isCompleted = image.status === 'completed'

        if (!isCompleted) {
          return (
            <View className='absolute inset-0 bg-black/60 items-center justify-center'>
              <Text className='text-white text-sm font-medium'>
                {image.status === 'pending' && '等待处理'}
                {image.status === 'processing' && '处理中'}
                {image.status === 'failed' && '处理失败'}
              </Text>
            </View>
          )
        }
        return null
      }}
    />
  )
}
