import React from 'react'
import {
  Modal as RNModal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ViewStyle,
  TextStyle,
} from 'react-native'
import { COLORS, FONT_SIZES, SPACING } from '../../constants'
import { Button } from './Button'

const { width: screenWidth, height: screenHeight } = Dimensions.get('window')

interface ModalProps {
  visible: boolean
  onClose: () => void
  title?: string
  children?: React.ReactNode
  showCloseButton?: boolean
  animationType?: 'none' | 'slide' | 'fade'
  style?: ViewStyle
  contentStyle?: ViewStyle
  titleStyle?: TextStyle
}

export const Modal: React.FC<ModalProps> = ({
  visible,
  onClose,
  title,
  children,
  showCloseButton = true,
  animationType = 'fade',
  style,
  contentStyle,
  titleStyle,
}) => {
  return (
    <RNModal
      visible={visible}
      transparent
      animationType={animationType}
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <View style={[styles.overlay, style]}>
        <TouchableOpacity style={styles.backdrop} activeOpacity={1} onPress={onClose} />
        <View style={[styles.content, contentStyle]}>
          {(title || showCloseButton) && (
            <View style={styles.header}>
              {title && <Text style={[styles.title, titleStyle]}>{title}</Text>}
              {showCloseButton && (
                <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                  <Text style={styles.closeText}>×</Text>
                </TouchableOpacity>
              )}
            </View>
          )}
          <View style={styles.body}>{children}</View>
        </View>
      </View>
    </RNModal>
  )
}

// 确认对话框
interface ConfirmModalProps {
  visible: boolean
  onClose: () => void
  onConfirm: () => void
  title?: string
  message: string
  confirmText?: string
  cancelText?: string
  confirmButtonVariant?: 'primary' | 'secondary' | 'outline'
}

export const ConfirmModal: React.FC<ConfirmModalProps> = ({
  visible,
  onClose,
  onConfirm,
  title = '确认',
  message,
  confirmText = '确定',
  cancelText = '取消',
  confirmButtonVariant = 'primary',
}) => {
  const handleConfirm = () => {
    onConfirm()
    onClose()
  }

  return (
    <Modal visible={visible} onClose={onClose} title={title} showCloseButton={false}>
      <Text style={styles.message}>{message}</Text>
      <View style={styles.buttonRow}>
        <Button title={cancelText} onPress={onClose} variant='outline' style={styles.button} />
        <Button
          title={confirmText}
          onPress={handleConfirm}
          variant={confirmButtonVariant}
          style={styles.button}
        />
      </View>
    </Modal>
  )
}

// 底部弹出模态框
interface BottomModalProps {
  visible: boolean
  onClose: () => void
  title?: string
  children?: React.ReactNode
  height?: number
}

export const BottomModal: React.FC<BottomModalProps> = ({
  visible,
  onClose,
  title,
  children,
  height = screenHeight * 0.6,
}) => {
  return (
    <RNModal
      visible={visible}
      transparent
      animationType='slide'
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <View style={styles.bottomOverlay}>
        <TouchableOpacity style={styles.bottomBackdrop} activeOpacity={1} onPress={onClose} />
        <View style={[styles.bottomContent, { height }]}>
          <View style={styles.bottomHandle} />
          {title && (
            <View style={styles.bottomHeader}>
              <Text style={styles.bottomTitle}>{title}</Text>
            </View>
          )}
          <View style={styles.bottomBody}>{children}</View>
        </View>
      </View>
    </RNModal>
  )
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  content: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    maxWidth: screenWidth * 0.9,
    maxHeight: screenHeight * 0.8,
    minWidth: 280,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SPACING.MD,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  title: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    flex: 1,
  },
  closeButton: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: SPACING.SM,
  },
  closeText: {
    fontSize: 24,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: '300',
  },
  body: {
    padding: SPACING.MD,
  },
  message: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    lineHeight: 24,
    marginBottom: SPACING.LG,
    textAlign: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    gap: SPACING.SM,
  },
  button: {
    flex: 1,
  },

  // Bottom modal styles
  bottomOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  bottomBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  bottomContent: {
    backgroundColor: COLORS.SURFACE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  bottomHandle: {
    width: 40,
    height: 4,
    backgroundColor: COLORS.BORDER,
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: SPACING.SM,
  },
  bottomHeader: {
    padding: SPACING.MD,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  bottomTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
  },
  bottomBody: {
    flex: 1,
    padding: SPACING.MD,
  },
})
