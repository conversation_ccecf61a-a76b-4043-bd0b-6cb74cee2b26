import React from 'react'
import {
  View,
  Text,
  ActivityIndicator,
  StyleSheet,
  Modal,
  ViewStyle,
  TextStyle,
} from 'react-native'
import { COLORS, FONT_SIZES, SPACING } from '../../constants'

interface LoadingProps {
  visible?: boolean
  text?: string
  size?: 'small' | 'large'
  color?: string
  overlay?: boolean
  style?: ViewStyle
  textStyle?: TextStyle
}

export const Loading: React.FC<LoadingProps> = ({
  visible = true,
  text = '加载中...',
  size = 'large',
  color = COLORS.PRIMARY,
  overlay = false,
  style,
  textStyle,
}) => {
  const content = (
    <View style={[styles.container, style]}>
      <ActivityIndicator size={size} color={color} />
      {text && <Text style={[styles.text, textStyle]}>{text}</Text>}
    </View>
  )

  if (overlay) {
    return (
      <Modal transparent visible={visible} animationType='fade' statusBarTranslucent>
        <View style={styles.overlay}>
          <View style={styles.modal}>{content}</View>
        </View>
      </Modal>
    )
  }

  return visible ? content : null
}

// 全屏加载组件
export const FullScreenLoading: React.FC<{
  visible?: boolean
  text?: string
}> = ({ visible = true, text = '加载中...' }) => {
  if (!visible) return null

  return (
    <View style={styles.fullScreen}>
      <Loading text={text} size='large' />
    </View>
  )
}

// 内联加载组件
export const InlineLoading: React.FC<{
  visible?: boolean
  text?: string
  size?: 'small' | 'large'
}> = ({ visible = true, text, size = 'small' }) => {
  if (!visible) return null

  return (
    <View style={styles.inline}>
      <ActivityIndicator size={size} color={COLORS.PRIMARY} />
      {text && <Text style={styles.inlineText}>{text}</Text>}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.MD,
  },
  text: {
    marginTop: SPACING.SM,
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  modal: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: SPACING.LG,
    minWidth: 120,
    alignItems: 'center',
  },
  fullScreen: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
    alignItems: 'center',
    justifyContent: 'center',
  },
  inline: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.SM,
  },
  inlineText: {
    marginLeft: SPACING.SM,
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
})
