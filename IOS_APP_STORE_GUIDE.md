# iOS App Store 上架完整指南

## 📋 目录
1. [开发者账号准备](#开发者账号准备)
2. [App Store Connect 配置](#app-store-connect-配置)
3. [内购产品配置](#内购产品配置)
4. [应用构建和上传](#应用构建和上传)
5. [审核准备](#审核准备)
6. [后端服务器配置](#后端服务器配置)
7. [测试流程](#测试流程)
8. [上架后维护](#上架后维护)

## 🔐 开发者账号准备

### 1. Apple Developer Program
- **费用**: $99/年
- **注册地址**: https://developer.apple.com/programs/
- **所需材料**:
  - Apple ID
  - 信用卡或借记卡
  - 企业注册需要D-U-N-S号码

### 2. 开发者账号类型选择
- **个人开发者**: 适合个人开发者，应用显示个人姓名
- **企业开发者**: 适合公司，应用显示公司名称
- **企业内部分发**: 仅限企业内部使用，不在App Store发布

## 🏪 App Store Connect 配置

### 1. 创建应用
1. 登录 [App Store Connect](https://appstoreconnect.apple.com)
2. 点击"我的App" → "+"号 → "新建App"
3. 填写基本信息:
   - **平台**: iOS
   - **名称**: 吉卜力风格AI (或您的应用名称)
   - **主要语言**: 简体中文
   - **套装ID**: com.yourcompany.ghibli-ai
   - **SKU**: 唯一标识符，如 GHIBLI_AI_2024

### 2. 应用信息配置
```
应用名称: 吉卜力风格AI
副标题: AI图片风格转换工具
类别: 摄影与录像 / 图形和设计
内容分级: 4+（无限制内容）
```

### 3. 定价和销售范围
- **价格**: 免费（内购收费）
- **销售范围**: 选择要发布的国家/地区
- **内购**: 启用

## 💰 内购产品配置

### 1. 创建内购产品
在App Store Connect中：
1. 进入您的应用
2. 点击"功能" → "App内购买项目"
3. 点击"+"创建新的内购项目

### 2. 内购产品类型
```typescript
// 推荐的产品ID命名规范
const PRODUCT_IDS = {
  POINTS_100: 'com.yourcompany.ghibli.points.100',
  POINTS_500: 'com.yourcompany.ghibli.points.500',
  POINTS_1000: 'com.yourcompany.ghibli.points.1000',
  VIP_MONTHLY: 'com.yourcompany.ghibli.vip.monthly',
  VIP_YEARLY: 'com.yourcompany.ghibli.vip.yearly',
}
```

### 3. 内购产品配置示例
```
产品ID: com.yourcompany.ghibli.points.100
类型: 消耗型项目
参考名称: 100积分包
显示名称: 100积分
描述: 获得100个创作积分，用于AI图片生成
价格: ¥6.00
```

### 4. 本地化信息
为每个支持的语言添加：
- 显示名称
- 描述
- 截图（如需要）

## 🔨 应用构建和上传

### 1. 配置 app.json
```json
{
  "expo": {
    "name": "吉卜力风格AI",
    "slug": "ghibli-ai",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "userInterfaceStyle": "dark",
    "splash": {
      "image": "./assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#111827"
    },
    "ios": {
      "bundleIdentifier": "com.yourcompany.ghibli.ai",
      "buildNumber": "1",
      "supportsTablet": true,
      "infoPlist": {
        "NSCameraUsageDescription": "需要访问相机来拍摄照片进行AI处理",
        "NSPhotoLibraryUsageDescription": "需要访问相册来选择照片进行AI处理"
      }
    },
    "plugins": [
      "expo-in-app-purchases"
    ]
  }
}
```

### 2. 构建应用
```bash
# 安装EAS CLI
npm install -g @expo/eas-cli

# 登录Expo账号
eas login

# 配置构建
eas build:configure

# 构建iOS应用
eas build --platform ios
```

### 3. 上传到App Store Connect
```bash
# 使用EAS Submit上传
eas submit --platform ios
```

## 📝 审核准备

### 1. 应用截图要求
- **iPhone 6.7"**: 1290 x 2796 像素 (必需)
- **iPhone 6.5"**: 1242 x 2688 像素 (必需)
- **iPhone 5.5"**: 1242 x 2208 像素
- **iPad Pro 12.9"**: 2048 x 2732 像素 (如支持iPad)

### 2. 应用描述
```
【应用描述示例】
🎨 吉卜力风格AI - 让你的照片充满魔法

将普通照片转换成宫崎骏动画风格的艺术作品！

✨ 主要功能：
• AI智能识别，一键转换风格
• 多种吉卜力经典场景风格
• 高清输出，支持保存和分享
• VIP会员享受更多专属风格

🎯 适用场景：
• 个人头像制作
• 社交媒体分享
• 艺术创作灵感
• 礼物制作

💎 会员特权：
• 解锁所有风格滤镜
• 无限次数使用
• 高清无水印输出
• 优先处理队列
```

### 3. 关键词优化
```
AI, 动漫, 宫崎骏, 吉卜力, 照片编辑, 风格转换, 艺术, 滤镜
```

### 4. 审核注意事项
- **内购说明**: 详细说明内购项目的用途
- **隐私政策**: 必须提供隐私政策链接
- **用户协议**: 提供服务条款
- **演示账号**: 如有登录要求，提供测试账号
- **审核备注**: 详细说明应用功能和使用方法

## 🖥️ 后端服务器配置

### 1. 收据验证服务
```typescript
// 服务器端收据验证
export async function verifyIOSReceipt(receiptData: string, isProduction: boolean) {
  const verifyURL = isProduction 
    ? 'https://buy.itunes.apple.com/verifyReceipt'
    : 'https://sandbox.itunes.apple.com/verifyReceipt'
  
  const response = await fetch(verifyURL, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      'receipt-data': receiptData,
      'password': 'your-app-specific-shared-secret'
    })
  })
  
  return response.json()
}
```

### 2. 数据库设计
```sql
-- 购买记录表
CREATE TABLE purchases (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  product_id VARCHAR(255) NOT NULL,
  transaction_id VARCHAR(255) UNIQUE NOT NULL,
  platform ENUM('ios', 'android') NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) NOT NULL,
  status ENUM('pending', 'verified', 'failed') DEFAULT 'pending',
  receipt_data TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  verified_at TIMESTAMP NULL,
  INDEX idx_user_id (user_id),
  INDEX idx_transaction_id (transaction_id)
);

-- 用户积分记录表
CREATE TABLE user_points_log (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  points_change INT NOT NULL,
  reason VARCHAR(255) NOT NULL,
  purchase_id BIGINT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id)
);
```

## 🧪 测试流程

### 1. 沙盒测试
1. 在App Store Connect创建沙盒测试用户
2. 在测试设备上登录沙盒账号
3. 测试所有内购流程
4. 验证收据验证逻辑

### 2. TestFlight测试
1. 上传构建版本到TestFlight
2. 邀请内部测试人员
3. 收集反馈并修复问题
4. 准备正式发布

### 3. 测试检查清单
- [ ] 所有内购产品可以正常购买
- [ ] 购买后积分/VIP状态正确更新
- [ ] 恢复购买功能正常
- [ ] 网络异常情况处理
- [ ] 用户取消购买处理
- [ ] 收据验证流程完整

## 📱 上架后维护

### 1. 监控指标
- 下载量和安装量
- 内购转化率
- 用户评分和评论
- 崩溃率和性能指标

### 2. 版本更新
- 定期更新应用功能
- 修复用户反馈的问题
- 适配新的iOS版本
- 优化用户体验

### 3. 客服支持
- 设置用户反馈渠道
- 及时回复用户问题
- 处理退款请求
- 维护用户关系

## ⚠️ 常见问题和解决方案

### 1. 审核被拒常见原因
- **内购描述不清**: 详细说明内购项目用途
- **隐私政策缺失**: 提供完整的隐私政策
- **功能不完整**: 确保所有功能都能正常使用
- **界面问题**: 修复UI/UX问题

### 2. 内购问题排查
- 检查产品ID是否正确
- 验证沙盒测试账号
- 确认收据验证逻辑
- 检查网络连接和错误处理

### 3. 性能优化
- 图片处理优化
- 网络请求优化
- 内存使用优化
- 启动时间优化

---

## 📞 技术支持

如果在上架过程中遇到问题，可以参考：
- [Apple Developer Documentation](https://developer.apple.com/documentation/)
- [App Store Connect Help](https://help.apple.com/app-store-connect/)
- [Expo Documentation](https://docs.expo.dev/)

祝您的应用顺利上架App Store！🎉
