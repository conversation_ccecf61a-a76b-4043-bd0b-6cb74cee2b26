{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/screens/*": ["src/screens/*"], "@/hooks/*": ["src/hooks/*"], "@/utils/*": ["src/utils/*"], "@/store/*": ["src/store/*"], "@/services/*": ["src/services/*"], "@/types/*": ["src/types/*"], "@/constants/*": ["src/constants/*"], "@/navigation/*": ["src/navigation/*"]}}}