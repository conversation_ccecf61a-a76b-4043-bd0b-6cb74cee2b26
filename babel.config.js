module.exports = function (api) {
  api.cache(true)
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      'nativewind/babel',
      [
        'module-resolver',
        {
          root: ['./src'],
          alias: {
            '@': './src',
            '@/components': './src/components',
            '@/screens': './src/screens',
            '@/hooks': './src/hooks',
            '@/utils': './src/utils',
            '@/store': './src/store',
            '@/services': './src/services',
            '@/types': './src/types',
            '@/constants': './src/constants',
            '@/navigation': './src/navigation',
          },
        },
      ],
    ],
  }
}
