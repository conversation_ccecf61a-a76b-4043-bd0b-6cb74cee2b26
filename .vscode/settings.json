{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "editor.tabSize": 2, "editor.insertSpaces": true, "files.eol": "\n", "prettier.semi": false, "prettier.singleQuote": true, "prettier.trailingComma": "es5"}