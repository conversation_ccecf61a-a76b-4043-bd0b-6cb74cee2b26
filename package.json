{"name": "image-generate-app", "version": "1.0.0", "main": "index.ts", "description": "基于AI的图片风格转换App", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx}\" \"*.{ts,tsx,js,json}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx}\" \"*.{ts,tsx,js,json}\""}, "dependencies": {"@invertase/react-native-apple-authentication": "^2.4.0", "@react-native-async-storage/async-storage": "^2.1.0", "@react-navigation/bottom-tabs": "^7.1.0", "@react-navigation/native": "^7.0.15", "@react-navigation/stack": "^7.1.1", "axios": "^1.11.0", "expo": "~53.0.20", "expo-in-app-purchases": "^14.5.0", "expo-status-bar": "~2.2.3", "nativewind": "^2.0.11", "react": "19.0.0", "react-native": "0.79.5", "react-native-device-info": "^14.0.0", "react-native-gesture-handler": "^2.21.2", "react-native-image-picker": "^7.2.0", "react-native-safe-area-context": "^5.0.0", "react-native-screens": "^4.3.0", "react-native-vector-icons": "^10.2.0", "tailwindcss": "^3.3.0", "zustand": "^5.0.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@eslint/eslintrc": "^3.3.1", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.2", "@types/react": "~19.0.10", "@types/react-native-vector-icons": "^6.4.18", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "babel-plugin-module-resolver": "^5.0.2", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "jest": "^30.0.5", "prettier": "^3.6.2", "typescript": "~5.8.3"}, "private": true}