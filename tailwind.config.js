/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./App.{js,jsx,ts,tsx}', './src/**/*.{js,jsx,ts,tsx}'],
  theme: {
    extend: {
      colors: {
        // 参考截图的色值
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
        },
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
          950: '#030712',
        },
        // 深色主题色值 - 参考截图
        dark: {
          bg: '#1a1a1a', // 主背景色
          card: '#2a2a2a', // 卡片背景色
          border: '#3a3a3a', // 边框色
          text: {
            primary: '#ffffff', // 主文字色
            secondary: '#a3a3a3', // 次要文字色
            muted: '#6b7280', // 弱化文字色
          },
        },
        // VIP卡片色值
        vip: {
          bg: '#fef3c7', // VIP卡片背景 - 浅橙色
          text: '#92400e', // VIP文字色 - 深橙色
          button: '#1f2937', // VIP按钮色 - 深灰色
        },
      },
      fontFamily: {
        sans: ['System'],
      },
      spacing: {
        18: '4.5rem',
        88: '22rem',
      },
      borderRadius: {
        '4xl': '2rem',
      },
      boxShadow: {
        glow: '0 0 20px rgba(59, 130, 246, 0.5)',
        'glow-lg': '0 0 40px rgba(59, 130, 246, 0.3)',
      },
    },
  },
  plugins: [],
}
