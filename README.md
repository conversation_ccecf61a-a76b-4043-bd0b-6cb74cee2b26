# 吉卜力风格AI - React Native App

基于AI的图片风格转换应用，支持将普通照片转换成吉卜力动画风格。

## 功能特性

### 🎨 核心功能
- **风格转换**: 支持多种吉卜力动画风格
- **智能瀑布流**: 支持2:3、3:2、1:1等多种比例的图片展示
- **实时预览**: 图片生成进度实时跟踪
- **作品管理**: 个人作品画廊，支持查看、删除、分享

### 🔐 用户系统
- **多种登录方式**: 手机号验证码登录、Apple ID登录
- **权限控制**: 未登录用户可浏览，操作需要登录
- **会员系统**: VIP会员享受更多特权

### 📱 技术特性
- **短路径解析**: 使用 `@/` 别名，告别复杂的相对路径
- **智能瀑布流**: 自动适配不同比例的图片布局
- **TypeScript**: 完整的类型安全
- **状态管理**: 使用 Zustand 进行状态管理
- **代码规范**: ESLint + Prettier，无分号风格

## 项目结构

```
src/
├── components/          # 组件
│   ├── ui/             # 通用UI组件
│   └── common/         # 业务组件
├── screens/            # 页面
│   ├── Home/           # 主页
│   ├── Gallery/        # 画廊
│   ├── Profile/        # 个人中心
│   ├── Login/          # 登录
│   └── Membership/     # 会员中心
├── hooks/              # 自定义Hooks
├── utils/              # 工具函数
├── store/              # 状态管理
├── services/           # API服务
├── types/              # 类型定义
├── constants/          # 常量
└── navigation/         # 导航配置
```

## 短路径配置

项目配置了路径别名，可以使用短路径导入：

```typescript
// ❌ 旧方式 - 复杂的相对路径
import { Button } from '../../components/ui'
import { useAuthStore } from '../../../store'

// ✅ 新方式 - 简洁的短路径
import { Button } from '@/components/ui'
import { useAuthStore } from '@/store'
```

### 可用的路径别名

- `@/*` → `src/*`
- `@/components/*` → `src/components/*`
- `@/screens/*` → `src/screens/*`
- `@/hooks/*` → `src/hooks/*`
- `@/utils/*` → `src/utils/*`
- `@/store/*` → `src/store/*`
- `@/services/*` → `src/services/*`
- `@/types/*` → `src/types/*`
- `@/constants/*` → `src/constants/*`
- `@/navigation/*` → `src/navigation/*`

## 智能瀑布流

### 支持的图片比例

- **2:3** - 竖版图片（常用于人像）
- **3:2** - 横版图片（常用于风景）
- **1:1** - 正方形图片
- **16:9** - 宽屏比例
- **9:16** - 高屏比例

### 使用方式

```typescript
import { StyleWaterfallGrid, GalleryWaterfallGrid } from '@/components/common'

// 风格展示瀑布流
<StyleWaterfallGrid
  styles={styles}
  onStylePress={handleStyleSelect}
  columnCount={2}
/>

// 图片画廊瀑布流
<GalleryWaterfallGrid
  images={images}
  onImagePress={handleImagePress}
  onImageLongPress={handleImageLongPress}
  columnCount={2}
/>
```

### 瀑布流特性

- **智能布局**: 自动计算最佳列分配
- **比例适配**: 根据图片比例自动调整高度
- **性能优化**: 虚拟化长列表支持
- **响应式**: 支持不同屏幕尺寸

## 开发命令

```bash
# 安装依赖
npm install

# 启动开发服务器
npm start

# 代码格式化
npm run format

# 代码检查
npm run lint

# 自动修复代码问题
npm run lint:fix

# TypeScript类型检查
npx tsc --noEmit
```

## 代码规范

项目使用无分号的代码风格：

```typescript
// ✅ 推荐
const user = await getUserInfo()
const styles = await getStyles()

// ❌ 不推荐
const user = await getUserInfo();
const styles = await getStyles();
```

## API配置

在 `src/constants/index.ts` 中配置API地址：

```typescript
export const BASE_URL = 'https://convert.pub'
export const API_PREFIX = '/ai/api'
```

## 权限控制

应用实现了灵活的权限控制系统：

- **浏览权限**: 所有用户都可以浏览内容
- **操作权限**: 需要登录才能进行创作、购买等操作
- **VIP权限**: VIP用户享受额外特权

```typescript
import { useAuthGuard } from '@/hooks'

const { requireAuth } = useAuthGuard()

// 检查权限后执行操作
requireAuth(() => {
  // 需要登录的操作
}, {
  requireLogin: true,
  requirePoints: true,
  onUnauthorized: (reason, action) => {
    // 处理未授权情况
  }
})
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
